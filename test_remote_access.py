#!/usr/bin/env python3
"""
测试远程Docker部署的TAPD插件API访问
"""

import requests
import json
import sys
import time
from urllib.parse import urljoin

def test_cors_headers(base_url):
    """测试CORS头部配置"""
    print("🔍 测试CORS配置...")
    
    # 测试OPTIONS预检请求
    try:
        response = requests.options(
            urljoin(base_url, "/api/v1/reqAgent/tapd/parse"),
            headers={
                'Origin': 'chrome-extension://test',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout=10
        )
        
        print(f"OPTIONS请求状态码: {response.status_code}")
        print("CORS头部:")
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                print(f"  {header}: {value}")
        
        return response.status_code == 200 or response.status_code == 204
        
    except Exception as e:
        print(f"❌ OPTIONS请求失败: {e}")
        return False

def test_api_connectivity(base_url):
    """测试API连通性"""
    print("🌐 测试API连通性...")
    
    try:
        # 测试基础连接
        response = requests.get(urljoin(base_url, "/"), timeout=10)
        print(f"根路径访问状态码: {response.status_code}")
        
        # 测试API路径
        response = requests.get(urljoin(base_url, "/api/v1/reqAgent/tapd/list"), timeout=10)
        print(f"API列表访问状态码: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_tapd_parse_api(base_url):
    """测试TAPD解析API"""
    print("📝 测试TAPD解析API...")
    
    # 模拟插件发送的数据
    test_data = {
        "url": "https://www.tapd.cn/test/story/detail/123456",
        "title": "测试需求标题",
        "content": "<div>测试需求内容</div>",
        "cookies": "test_cookie=test_value",
        "user_id": 1,
        "project_id": 2
    }
    
    try:
        response = requests.post(
            urljoin(base_url, "/api/v1/reqAgent/tapd/parse"),
            json=test_data,
            headers={
                'Content-Type': 'application/json',
                'Origin': 'chrome-extension://test'
            },
            timeout=30
        )
        
        print(f"POST请求状态码: {response.status_code}")
        print(f"响应头部: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ API响应成功: {result.get('status', 'unknown')}")
                return True
            except:
                print("⚠️ 响应不是有效JSON")
                print(f"响应内容: {response.text[:200]}...")
        else:
            print(f"❌ API请求失败: {response.text[:200]}...")
            
        return False
        
    except Exception as e:
        print(f"❌ TAPD解析API测试失败: {e}")
        return False

def test_network_reachability(host, port):
    """测试网络可达性"""
    print(f"🔌 测试网络连接 {host}:{port}...")
    
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口 {port} 可达")
            return True
        else:
            print(f"❌ 端口 {port} 不可达")
            return False
            
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")
        return False

def generate_plugin_config(server_ip, port=9999):
    """生成插件配置建议"""
    print("\n📋 插件配置建议:")
    print("=" * 50)
    
    base_url = f"http://{server_ip}:{port}"
    api_url = f"{base_url}/api/v1/reqAgent/tapd/parse"
    
    print(f"应用接收URL: {api_url}")
    print(f"认证令牌: dev")
    print(f"服务器地址: {server_ip}")
    print(f"服务器端口: {port}")
    
    # 生成测试命令
    print(f"\n🧪 手动测试命令:")
    print(f"curl -X POST '{api_url}' \\")
    print(f"  -H 'Content-Type: application/json' \\")
    print(f"  -H 'Origin: chrome-extension://test' \\")
    print(f"  -d '{{\"url\":\"test\",\"title\":\"test\",\"content\":\"test\",\"user_id\":1,\"project_id\":2}}'")

def main():
    if len(sys.argv) != 2:
        print("使用方法: python test_remote_access.py <服务器IP>")
        print("例如: python test_remote_access.py ************")
        sys.exit(1)
    
    server_ip = sys.argv[1]
    port = 9999
    base_url = f"http://{server_ip}:{port}"
    
    print(f"🚀 测试远程Docker部署访问")
    print(f"服务器: {server_ip}:{port}")
    print(f"基础URL: {base_url}")
    print("=" * 50)
    
    # 执行测试
    tests = [
        ("网络可达性", lambda: test_network_reachability(server_ip, port)),
        ("API连通性", lambda: test_api_connectivity(base_url)),
        ("CORS配置", lambda: test_cors_headers(base_url)),
        ("TAPD解析API", lambda: test_tapd_parse_api(base_url))
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        results[test_name] = test_func()
        time.sleep(1)
    
    # 总结结果
    print("\n📊 测试结果总结:")
    print("=" * 50)
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    # 生成配置建议
    generate_plugin_config(server_ip, port)
    
    # 问题诊断
    if not all(results.values()):
        print("\n🔧 问题诊断建议:")
        print("=" * 50)
        
        if not results.get("网络可达性"):
            print("• 检查服务器防火墙设置")
            print("• 确认Docker容器正在运行")
            print("• 检查端口映射配置")
        
        if not results.get("CORS配置"):
            print("• 检查后端CORS中间件配置")
            print("• 确认允许Chrome扩展的Origin")
            print("• 重启Docker容器应用CORS修复")
        
        if not results.get("TAPD解析API"):
            print("• 检查API路由配置")
            print("• 查看Docker容器日志")
            print("• 确认数据库连接正常")

if __name__ == "__main__":
    main()

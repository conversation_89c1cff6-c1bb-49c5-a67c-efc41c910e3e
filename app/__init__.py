import logging
import os
import pathlib
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.responses import FileResponse
from starlette.middleware.cors import CORSMiddleware
from tortoise import Tortoise

from app.core.exceptions import SettingNotFound
from app.core.init_app import (
    init_data,
    make_middlewares,
    register_exceptions,
    register_routers,
)
from app.core.cors_middleware import CustomCORSMiddleware

# 配置日志
logger = logging.getLogger(__name__)

try:
    from app.settings.config import settings
except ImportError:
    raise SettingNotFound("Can not import settings")


from app.core.model_preload import start_model_preloading


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("应用启动中...")
    await init_data()

    # 在后台线程中预热嵌入模型，不阻塞应用启动
    start_model_preloading(in_background=True)

    logger.info("应用初始化完成")
    yield
    logger.info("应用关闭中...")
    await Tortoise.close_connections()
    logger.info("数据库连接已关闭")


def create_app() -> FastAPI:
    logger.info("创建FastAPI应用...")
    app = FastAPI(
        title=settings.APP_TITLE,
        description=settings.APP_DESCRIPTION,
        version=settings.VERSION,
        openapi_url="/openapi.json",
        middleware=make_middlewares(),
        lifespan=lifespan,
    )

    # 只使用自定义CORS中间件，避免重复的CORS头部
    app.add_middleware(CustomCORSMiddleware)

    # 添加TAPD插件下载路由
    @app.get("/download/tapd-plugin")
    async def download_tapd_plugin():
        """下载TAPD页面提取插件"""
        plugin_file = pathlib.Path("app/static/tapd页面提取插件.zip")
        if plugin_file.exists():
            return FileResponse(
                path=str(plugin_file),
                filename="tapd页面提取插件.zip",
                media_type="application/zip"
            )
        else:
            logger.error(f"TAPD插件文件不存在: {plugin_file}")
            return {"error": "插件文件不存在"}

    register_exceptions(app)
    register_routers(app, prefix="/api")
    logger.info("FastAPI应用创建完成")
    return app


app = create_app()

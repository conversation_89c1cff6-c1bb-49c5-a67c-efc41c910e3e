import shutil
from fastapi import APIRouter, HTTPException, status, Request, Depends
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import logging
import re
import json
import uuid
import os
import base64
from datetime import datetime
from bs4 import BeautifulSoup
import markdownify  # 需要先安装: pip install markdownify
from app.core.dependency import DependPermisson
from app.api.v1.reqAgent.image_markdown_converter import html_to_markdown_with_image_analysis

# 检查是否安装了lxml库
try:
    from lxml import etree
    LXML_AVAILABLE = True
except ImportError:
    LXML_AVAILABLE = False
    logging.warning("lxml库未安装，无法使用XPath功能。请使用 pip install lxml 安装。")

# 设置日志
logger = logging.getLogger(__name__)
router = APIRouter(tags=["TAPD需求解析智能体"])

# 内存缓存，用于存储解析后的需求数据
tapd_requirements_cache = {}

class TapdContentRequest(BaseModel):
    url: str
    title: str
    content: str
    cookies: Optional[str] = None
    user_id: int
    project_id: int

class RequirementInfo(BaseModel):
    id: str  # 缓存ID，非TAPD ID
    tapd_id: Optional[str] = None  # TAPD中的需求ID
    name: str  # 需求名称
    content: str  # 需求内容
    handler: Optional[str] = None  # 处理人
    developer: Optional[str] = None  # 开发人员
    tester: Optional[str] = None  # 测试人员
    url: str  # 原始TAPD URL
    status: Optional[str] = None  # 需求状态
    priority: Optional[str] = None  # 优先级

class TapdContentResponse(BaseModel):
    status: str
    message: str
    data: Optional[RequirementInfo] = None

class RequirementsListResponse(BaseModel):
    status: str
    message: str
    data: List[RequirementInfo] = []

@router.options("/parse")
async def options_tapd_content():
    """
    处理OPTIONS预检请求，确保CORS正常工作
    """
    # 这个函数不需要实际执行任何操作，FastAPI会自动添加正确的CORS头
    # 但我们可以添加日志以便调试
    logger.info("收到TAPD内容解析的OPTIONS预检请求")
    return {}

@router.post("/parse", response_model=TapdContentResponse)
async def parse_tapd_content(request: Request):
    # 记录请求头，用于调试CORS问题
    headers = dict(request.headers)
    logger.info(f"接收到TAPD内容解析请求，请求头: {headers}")

    data = await request.json()
    url = data.get('url', '')
    title = data.get('title', '')
    content = data.get('content', '')

    # 从请求体中获取token，而不是从请求头中获取
    token = data.get('token', 'dev')
    logger.info(f"从请求体中获取token: {token}")
    logger.info(f"接收到TAPD内容，长度: {len(content)}")

    # 将原始内容写入文件以便调试
    with open("tapd_content_raw.txt", "w", encoding="utf-8") as f:
        f.write(content)

    # 预处理TAPD特有的内容格式
    processed_content = process_tapd_content(content)
    with open("tapd_content_raw2.txt", "w", encoding="utf-8") as f:
        f.write(content)
    # 转换为Markdown并解析需求
    requirement_info = parse_requirement(url, title, processed_content)
    # logger.info(f"解析后的需求数据: {requirement_info}")

    # 检查是否已存在相同URL的需求
    existing_req_id = None
    for req_id, req_info in tapd_requirements_cache.items():
        if req_info.url == url:
            existing_req_id = req_id
            logger.info(f"发现相同URL的需求，更新现有需求: {req_id}")
            break

    if existing_req_id:
        # 更新现有需求
        requirement_info.id = existing_req_id
        tapd_requirements_cache[existing_req_id] = requirement_info
        logger.info(f"更新了现有需求: {existing_req_id}")
    else:
        # 生成新的唯一ID并缓存
        req_id = str(uuid.uuid4())
        requirement_info.id = req_id
        tapd_requirements_cache[req_id] = requirement_info
        logger.info(f"创建了新需求: {req_id}")

    return TapdContentResponse(
        status="success",
        data=requirement_info,
        message="Success"
    )

@router.get("/list")
async def list_requirements():
    """
    获取所有缓存的需求列表
    """
    try:
        requirements = list(tapd_requirements_cache.values())
        logger.info(f"获取需求列表，缓存中共有{len(tapd_requirements_cache)}个需求")

        # 添加测试数据，确保始终有数据返回
        if not requirements:
            test_req = RequirementInfo(
                id="test-123",
                tapd_id="12345",
                name="测试需求",
                content="这是一个测试需求的内容。",
                handler="测试处理人",
                developer="测试开发者",
                tester="测试测试员",
                url="https://www.tapd.cn/test",
                status="进行中",
                priority="高"
            )
            requirements = [test_req]
            logger.info("添加了测试需求数据")

        # 直接返回需求列表，而不是包装在RequirementsListResponse中
        return requirements

    except Exception as e:
        logger.error(f"获取需求列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求列表失败: {str(e)}"
        )

@router.delete("/delete/{req_id}", response_model=dict)
async def delete_requirement(req_id: str):
    """
    删除指定的需求缓存
    """
    try:
        if req_id in tapd_requirements_cache:
            del tapd_requirements_cache[req_id]
            return {
                "status": "success",
                "message": f"需求 {req_id} 已成功删除"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"需求 {req_id} 不存在"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除需求失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除需求失败: {str(e)}"
        )

@router.delete("/clear", response_model=dict)
async def clear_all_requirements():
    """
    清除所有需求缓存
    """
    try:
        count = len(tapd_requirements_cache)
        tapd_requirements_cache.clear()
        logger.info(f"已清除所有{count}个需求缓存")
        return {
            "status": "success",
            "message": f"已清除所有{count}个需求",
            "count": count
        }
    except Exception as e:
        logger.error(f"清除所有需求失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除所有需求失败: {str(e)}"
        )

def html_to_markdown(html_content: str) -> str:
    """将HTML内容转换为Markdown格式，并优化文本格式和图片理解"""
    try:
        # 强制启用图片分析
        os.environ["ENABLE_IMAGE_ANALYSIS"] = "true"

        # 使用BeautifulSoup预处理HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 移除不需要的标签
        for script in soup(["script", "style"]):
            script.decompose()

        # 优化文本段落
        for p in soup.find_all(['p', 'div', 'span']):
            if p.string and len(p.string.strip()) > 0:
                # 添加句号，如果文本末尾没有标点符号
                text = p.string.strip()
                if text and len(text) > 5 and not text[-1] in ['\u3002', '\uff01', '\uff1f', '.', '!', '?', '\uff1b', '\u3001', ';', '\uff0c', ',']:
                    p.string = text + '\u3002'  # 添加中文句号

        # 优化图片标签，确保有alt属性
        for img in soup.find_all('img'):
            if not img.get('alt') or img['alt'].strip() == '':
                # 如果没有alt文本，添加一个默认的
                img['alt'] = '需求文档图片'

        # 使用支持图片分析的Markdown转换器
        logger.info(f"开始进入html_to_markdown_with_image_analysis")
        markdown_text = html_to_markdown_with_image_analysis(
            str(soup),
            image_analysis_enabled=True,
            # 使用阿里云通义千问视觉模型的环境变量
            api_key=os.environ.get("QWEN_VL_API_KEY", "sk-85477c3eb0424bb89d5421d2b28d2051"),
            api_base=os.environ.get("QWEN_VL_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
            model=os.environ.get("QWEN_VL_MODEL", "qwen-vl-plus-latest")
        )

        # 进一步优化文本格式
        markdown_text = optimize_markdown_text(markdown_text)

        return markdown_text.strip()
    except Exception as e:
        logger.error(f"HTML转Markdown失败: {str(e)}")
        return html_content  # 转换失败时返回原始内容

def optimize_markdown_text(markdown_text: str) -> str:
    """优化Markdown文本格式，添加标点符号和格式化"""
    try:
        # 处理没有标点符号的行
        lines = markdown_text.split('\n')
        result_lines = []

        for line in lines:
            line = line.rstrip()
            if not line:  # 空行
                result_lines.append(line)
                continue

            # 如果是标题行或列表项，不添加标点
            if line.startswith('#') or line.startswith('-') or line.startswith('*') or line.startswith('1.') or \
               line.startswith('![') or line.startswith('```') or line.startswith('<!--'):
                result_lines.append(line)
                continue

            # 如果行尾没有标点符号且长度足够，添加句号
            if len(line) > 5 and not line[-1] in ['。', '！', '？', '.', '!', '?', '；', '、', ';', '，', ',']:
                line += '。'  # 添加中文句号

            result_lines.append(line)

        # 合并行
        result_text = '\n'.join(result_lines)

        # 处理过多的空行
        result_text = re.sub(r'\n{3,}', '\n\n', result_text)

        return result_text
    except Exception as e:
        logger.error(f"优化Markdown文本失败: {str(e)}")
        return markdown_text  # 处理失败时返回原始文本

def process_tapd_content(html_content: str) -> str:
    """专门处理TAPD内容的特殊格式，返回处理后的内容"""
    try:
        # 使用BeautifulSoup预处理HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 处理带有alt="需求文档图片"属性的图片
        req_images = soup.find_all('img', alt="需求文档图片")
        logger.info(f"找到 {len(req_images)} 张需求文档图片")

        # 确保输出目录存在
        output_dir = "tapd_images"
        #判断目录是否存在，存在则删除（包括里面的文件）
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
        os.makedirs(output_dir, exist_ok=True)

        # 处理每张需求文档图片
        for i, img in enumerate(req_images):
            try:
                # 获取src属性
                img_src = img.get('src')
                if not img_src:
                    logger.warning(f"第{i+1}张需求文档图片没有src属性")
                    continue
                local_path=''
                # 保存图片到本地
                # local_path = save_base64_image(img_src, output_dir, f"req_doc_img_{i+1}")
                logger.info(f"已保存第{i+1}张需求文档图片到: {local_path}")

                if local_path:
                    # 将本地路径替换原始src值
                    img['src'] = local_path
                    logger.info(f"已将第{i+1}张需求文档图片的src替换为本地路径: {local_path}")
                else:
                    logger.warning(f"第{i+1}张需求文档图片保存失败")
            except Exception as e:
                logger.error(f"处理第{i+1}张需求文档图片时出错: {str(e)}")
                continue

        # 返回处理后的HTML内容
        return str(soup)
    except Exception as e:
        logger.error(f"处理TAPD内容时出错: {str(e)}")
        return html_content  # 处理失败时返回原始内容

def parse_requirement(url: str, title: str, html_content: str) -> RequirementInfo:
    """
    解析TAPD内容，提取需求信息
    """
    # 提取TAPD ID
    tapd_id = ""
    id_match = re.search(r'/(\d+)$', url)
    if id_match:
        tapd_id = id_match.group(1)

    # 初始化结果
    result = RequirementInfo(
        id="",  # 将在外部设置
        tapd_id=tapd_id,
        name=title,
        content="",
        handler="",
        developer="",
        tester="",
        url=url,
        status="",
        priority=""
    )

    try:
        logger.info(f"开始解析需求内容，标题: {title}, URL: {url}")
        # 如果传入的是JSON字符串，尝试解析
        try:
            data = json.loads(html_content)
            logger.info(f"解析JSON数据: {data.keys() if isinstance(data, dict) else 'not a dict'}")

            if isinstance(data, dict):
                # 直接从字段中提取数据
                # 根据要求，使用指定的字段名
                if 'content' in data:
                    # 将HTML内容转换为Markdown
                    result.content = html_to_markdown(data['content'])
                    logger.info(f"从 content 字段提取内容并转换为Markdown")
                if 'ownerner' in data:  # 处理人字段为 ownerner
                    result.handler = data['ownerner']
                    logger.info(f"从 ownerner 字段提取处理人: {data['ownerner']}")
                if 'developer' in data:  # 开发人员字段为 developer
                    result.developer = data['developer']
                    logger.info(f"从 developer 字段提取开发人员: {data['developer']}")
                # 尝试从多个可能的字段中提取测试人员信息
                tester_fields = ['custom_field_three', 'tester', 'qa', 'test_owner', '测试人员']
                for field in tester_fields:
                    if field in data and data[field]:
                        result.tester = data[field]
                        logger.info(f"从 {field} 字段提取测试人员: {data[field]}")
                        break
                if 'status' in data:
                    result.status = data['status']
                if 'priority' in data:
                    result.priority = data['priority']

                # 如果有详细信息选项卡字段，优先使用
                if 'detailInfo' in data and data['detailInfo']:
                    detail_info = data['detailInfo']
                    logger.info(f"找到详细信息选项卡字段: {detail_info.keys() if isinstance(detail_info, dict) else 'not a dict'}")
                    if isinstance(detail_info, dict):
                        # 从详细信息选项卡中提取内容
                        if 'content' in detail_info:
                            result.content = html_to_markdown(detail_info['content'])
                            logger.info(f"从详细信息选项卡中提取内容并转换为Markdown")
                        # 其他字段仍然使用指定的字段名
                        if 'ownerner' in detail_info:
                            result.handler = detail_info['ownerner']
                            logger.info(f"从详细信息选项卡中提取处理人: {detail_info['ownerner']}")
                        if 'developer' in detail_info:
                            result.developer = detail_info['developer']
                            logger.info(f"从详细信息选项卡中提取开发人员: {detail_info['developer']}")
                        # 尝试从多个可能的字段中提取测试人员信息
                        tester_fields = ['custom_field_three', 'tester', 'qa', 'test_owner', '测试人员']
                        for field in tester_fields:
                            if field in detail_info and detail_info[field]:
                                result.tester = detail_info[field]
                                logger.info(f"从详细信息选项卡的 {field} 字段提取测试人员: {detail_info[field]}")
                                break

                # 如果有字段映射，处理字段映射
                field_mapping = {
                    '详细信息': 'content',
                    '处理人': 'handler',  # 中文字段名映射
                    'ownerner': 'handler',  # 处理人字段为 ownerner
                    'owner': 'handler',  # 处理人字段可能为 owner
                    '开发人员': 'developer',  # 中文字段名映射
                    'developer': 'developer',  # 开发人员字段为 developer
                    'dev': 'developer',  # 开发人员字段可能为 dev
                    '测试人员': 'tester',  # 中文字段名映射
                    'custom_field_three': 'tester',  # 测试人员字段为 custom_field_three
                    'tester': 'tester',  # 测试人员字段可能为 tester
                    'qa': 'tester',  # 测试人员字段可能为 qa
                    'test_owner': 'tester',  # 测试人员字段可能为 test_owner
                    '状态': 'status',
                    '优先级': 'priority'
                }

                for key, value in data.items():
                    if key in field_mapping and value:
                        setattr(result, field_mapping[key], value)

                return result
        except json.JSONDecodeError:
            logger.info("非JSON格式，将使用HTML解析")
        except Exception as e:
            logger.error(f"JSON解析失败: {str(e)}")

        # 使用BeautifulSoup解析HTML内容
        soup = BeautifulSoup(html_content, 'html.parser')
        logger.info("使用BeautifulSoup解析HTML内容")

        # 1. 提取content - 从 div[class="detail-item__content-wrapper"] 元素
        content_divs = soup.find_all('div', attrs={'class': 'detail-item__content-wrapper'})
        if content_divs:
            # 将HTML内容转换为Markdown，并包含图片解析
            content_html = ''.join([str(div) for div in content_divs])
            result.content = html_to_markdown(content_html)
            logger.info(f"从 div[class=\"detail-item__content-wrapper\"] 提取到内容并转换为Markdown，长度: {len(result.content)}")
        else:
            logger.warning("未找到内容元素 div[class=\"detail-item__content-wrapper\"]")

            # 尝试其他可能的内容选择器
            desc_divs = soup.find_all('div', class_='description-content')
            if desc_divs:
                # 将HTML内容转换为Markdown，并包含图片解析
                content_html = ''.join([str(div) for div in desc_divs])
                result.content = html_to_markdown(content_html)
                logger.info(f"从 div.description-content 提取到内容并转换为Markdown，长度: {len(result.content)}")

        # 2. 提取developer - 从 span[field="developer"] 元素
        developer_spans = soup.find_all('span', attrs={'field': 'developer'})
        if developer_spans and len(developer_spans) > 0:
            result.developer = developer_spans[0].get_text(strip=True)
            logger.info(f"从 span[field=\"developer\"] 提取到开发人员: {result.developer}")
        else:
            logger.warning("未找到开发人员元素 span[field=\"developer\"]")

        # 3. 提取handler - 从 span[field="creator"] 元素
        handler_spans = soup.find_all('span', attrs={'field': 'creator'})
        if handler_spans and len(handler_spans) > 0:
            result.handler = handler_spans[0].get_text(strip=True)
            logger.info(f"从 span[field=\"creator\"] 提取到处理人: {result.handler}")
        else:
            logger.warning("未找到处理人元素 span[field=\"creator\"]")

        # 4. 提取tester - 尝试多种可能的元素选择器
        # 首先尝试 span[field="custom_field_three"] 元素
        tester_spans = soup.find_all('span', attrs={'field': 'custom_field_three'})
        if tester_spans and len(tester_spans) > 0:
            result.tester = tester_spans[0].get_text(strip=True)
            logger.info(f"从 span[field=\"custom_field_three\"] 提取到测试人员: {result.tester}")
        else:
            # 尝试使用XPath查找测试人员信息
            if LXML_AVAILABLE:
                try:
                    # 将BeautifulSoup对象转换为lxml对象
                    html_str = str(soup)
                    html_tree = etree.HTML(html_str)

                    # 使用XPath查找测试人员元素
                    xpath_expr = "//span[text()='测试人员']/ancestor::div[1]/following-sibling::div/span"
                    tester_elements = html_tree.xpath(xpath_expr)

                    if tester_elements and len(tester_elements) > 0:
                        tester_text = tester_elements[0].text.strip() if tester_elements[0].text else ""
                        if tester_text:
                            result.tester = tester_text
                            logger.info(f"使用XPath从页面中提取到测试人员: {result.tester}")
                    else:
                        # 尝试其他可能的XPath表达式
                        alternative_xpaths = [
                            "//div[contains(text(), '测试人员')]/following-sibling::div",
                            "//label[contains(text(), '测试人员')]/following-sibling::div",
                            "//td[contains(text(), '测试人员')]/following-sibling::td",
                            "//th[contains(text(), '测试人员')]/parent::tr/td[2]"
                        ]

                        for xpath in alternative_xpaths:
                            elements = html_tree.xpath(xpath)
                            if elements and len(elements) > 0:
                                tester_text = elements[0].text.strip() if elements[0].text else ""
                                if tester_text:
                                    result.tester = tester_text
                                    logger.info(f"使用备用XPath '{xpath}' 从页面中提取到测试人员: {result.tester}")
                                    break

                        if not result.tester:
                            logger.info("使用XPath未找到测试人员元素")
                except Exception as e:
                    logger.warning(f"使用XPath查找测试人员失败: {str(e)}")
            else:
                logger.warning("lxml库未安装，无法使用XPath功能")

            # 如果XPath方法失败，尝试使用BeautifulSoup查找
            if not result.tester:
                # 尝试查找包含"测试人员"文本的元素
                tester_labels = soup.find_all(string=lambda text: text and "测试人员" in text)
                for label in tester_labels:
                    # 查找标签后面的元素，可能包含测试人员信息
                    parent = label.parent
                    if parent:
                        # 尝试查找父元素的下一个兄弟元素
                        next_sibling = parent.find_next_sibling()
                        if next_sibling:
                            tester_text = next_sibling.get_text(strip=True)
                            if tester_text:
                                result.tester = tester_text
                                logger.info(f"从包含'测试人员'文本的元素附近提取到测试人员: {result.tester}")
                                break

            # 如果仍然没有找到，尝试查找其他可能的元素
            if not result.tester:
                # 尝试查找所有可能包含测试人员信息的元素
                tester_candidates = [
                    soup.find_all('span', attrs={'field': 'tester'}),
                    soup.find_all('span', attrs={'field': 'qa'}),
                    soup.find_all('span', attrs={'field': 'test_owner'}),
                    soup.find_all('div', class_='tester-info'),
                    soup.find_all('div', class_='qa-info')
                ]

                for candidates in tester_candidates:
                    if candidates and len(candidates) > 0:
                        tester_text = candidates[0].get_text(strip=True)
                        if tester_text:
                            result.tester = tester_text
                            logger.info(f"从备用选择器提取到测试人员: {result.tester}")
                            break

                # 如果仍然没有找到，尝试从表格中提取
                if not result.tester:
                    # 查找所有表格
                    tables = soup.find_all('table')
                    for table in tables:
                        # 查找表格中的所有行
                        rows = table.find_all('tr')
                        for row in rows:
                            # 查找行中的所有单元格
                            cells = row.find_all(['td', 'th'])
                            for i, cell in enumerate(cells):
                                # 检查单元格是否包含"测试人员"文本
                                if "测试人员" in cell.get_text():
                                    # 如果找到，尝试获取下一个单元格的文本
                                    if i + 1 < len(cells):
                                        tester_text = cells[i + 1].get_text(strip=True)
                                        if tester_text:
                                            result.tester = tester_text
                                            logger.info(f"从表格中提取到测试人员: {result.tester}")
                                            break
                            if result.tester:
                                break
                        if result.tester:
                            break

            if not result.tester:
                logger.warning("未找到测试人员信息，尝试了多种选择器但都失败了")

        # 如果还是没有找到内容，尝试更多的选择器
        if not result.content:
            # 尝试查找所有可能包含内容的元素
            content_candidates = [
                soup.find_all('div', class_='story-content'),
                soup.find_all('div', class_='requirement-content'),
                soup.find_all('div', class_='detail-content'),
                soup.find_all('pre', class_='code-block')
            ]

            for candidates in content_candidates:
                if candidates:
                    # 将HTML内容转换为Markdown，并包含图片解析
                    content_html = ''.join([str(div) for div in candidates])
                    if content_html:
                        result.content = html_to_markdown(content_html)
                        logger.info(f"从备用选择器提取到内容并转换为Markdown，长度: {len(result.content)}")
                        break

    except Exception as e:
        logger.error(f"解析需求内容时出错: {str(e)}")
        # 即使解析出错，也返回基本信息

    return result

def save_base64_image(base64_data: str, output_dir: str = "saved_images", filename_prefix: str = "image") -> str:
    """
    将Base64编码的图片解码并保存到本地

    Args:
        base64_data: Base64编码的图片数据
        output_dir: 输出目录，默认为"saved_images"
        filename_prefix: 文件名前缀，默认为"image"

    Returns:
        str: 保存的图片文件路径
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 检查是否是有效的Base64编码数据
        if not base64_data:
            logger.error("Base64数据为空")
            return ""

        # 确定图片格式和Base64数据
        image_format = "png"  # 默认格式
        if ',' in base64_data:
            # 数据格式类似于: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
            mime_part, base64_part = base64_data.split(',', 1)

            # 从MIME类型中提取图片格式
            if 'image/' in mime_part:
                mime_type = mime_part.split('image/')[1].split(';')[0]
                if mime_type in ['png', 'jpeg', 'jpg', 'gif', 'svg+xml', 'webp']:
                    image_format = 'svg' if mime_type == 'svg+xml' else mime_type

            base64_data = base64_part

        # 解码Base64数据
        try:
            image_data = base64.b64decode(base64_data)
        except Exception as decode_error:
            logger.error(f"Base64解码失败: {str(decode_error)}")
            # 尝试修复可能的Base64编码问题
            # 有时Base64数据可能包含空格或换行符
            base64_data = base64_data.replace(' ', '').replace('\n', '')
            try:
                image_data = base64.b64decode(base64_data)
            except Exception as retry_error:
                logger.error(f"修复后的Base64解码仍然失败: {str(retry_error)}")
                return ""

        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.{image_format}"
        filepath = os.path.join(output_dir, filename)

        # 保存图片
        with open(filepath, "wb") as f:
            f.write(image_data)

        # 验证保存的文件是否是有效的图片
        if os.path.getsize(filepath) == 0:
            logger.error(f"保存的图片文件大小为0: {filepath}")
            os.remove(filepath)  # 删除无效文件
            return ""

        logger.info(f"成功保存Base64图片到: {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"保存Base64图片失败: {str(e)}")
        return ""

def extract_and_save_images_from_html(html_content: str, output_dir: str = "saved_images") -> dict:
    """
    从HTML内容中提取所有图片的Base64编码，并保存到本地

    Args:
        html_content: HTML内容
        output_dir: 输出目录，默认为"saved_images"

    Returns:
        dict: 图片URL到本地文件路径的映射
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找所有图片标签
        images = soup.find_all('img')
        logger.info(f"在HTML内容中找到 {len(images)} 张图片")

        # 保存结果
        result = {}

        # 处理每张图片
        for i, img in enumerate(images):
            src = img.get('src', '')
            alt = img.get('alt', '')

            # 只处理Base64编码的图片
            if src.startswith('data:image'):
                # 生成文件名前缀
                prefix = f"image_{i+1}"
                if alt:
                    # 使用alt属性作为文件名前缀，但移除特殊字符
                    import re
                    prefix = re.sub(r'[^\w\s]', '', alt)[:30]  # 限制长度为30个字符
                    prefix = prefix.replace(' ', '_')

                # 保存图片
                filepath = save_base64_image(src, output_dir, prefix)
                if filepath:
                    result[src] = filepath
                    logger.info(f"保存图片 {i+1}/{len(images)}: {filepath}")
            else:
                logger.info(f"跳过非Base64图片 {i+1}/{len(images)}: {src[:50]}...")

        logger.info(f"成功保存 {len(result)} 张Base64图片到 {output_dir} 目录")
        return result
    except Exception as e:
        logger.error(f"提取和保存图片失败: {str(e)}")
        return {}

@router.post("/save-images", response_model=dict)
async def save_images_from_content(request: Request):
    """
    从TAPD内容中提取并保存所有Base64编码的图片
    """
    try:
        data = await request.json()
        content = data.get('content', '')
        output_dir = data.get('output_dir', 'saved_images')

        if not content:
            return {
                "status": "error",
                "message": "内容不能为空",
                "data": {}
            }

        # 提取并保存图片
        result = extract_and_save_images_from_html(content, output_dir)

        return {
            "status": "success",
            "message": f"成功保存 {len(result)} 张图片",
            "data": result
        }
    except Exception as e:
        logger.error(f"保存图片失败: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "message": f"保存图片失败: {str(e)}",
            "data": {}
        }

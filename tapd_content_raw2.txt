<div id="root" element-loading-spinner="tapd-loading"><!----><div class="el-dialog__wrapper" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog agi-dialog agi-dialog--small agi-dialog--no-footer " style="margin-top: 15vh;"><div class="el-dialog__header"><div class="agi-dialog__header agi-dialog__header--no-title"><i class="agi-icon tapd-icon-close-v2 agi-dialog__close-icon"></i></div><!----></div><!----><!----></div></div><!----><!----><div><!----><div class="el-dialog__wrapper degrade-dialog adjust-degrade-dialog license-adjust-degrade-dialog" width="800px" center="true" top="0" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog el-dialog--center agi-dialog agi-dialog--small " style="margin-top: 0px; width: 800px;"><div class="el-dialog__header"><div class="adjustMemberModalTitle"><div class="left"><span class="back-icon tapd-icon-return-v2"></span><span>成员授权调整</span></div><span class="close-icon tapd-icon-close-v2"></span></div><!----></div><!----><div class="el-dialog__footer"><div class="dialog-footer"><button type="button" class="contact-us-button agi-button agi-button--default agi-button--level-primary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">确定</span></button><button type="button" class="contact-us-button agi-button agi-button--plain agi-button--level-secondary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">联系我们</span></button></div></div></div></div><div class="el-dialog__wrapper sme-apply-dialog" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog" style="margin-top: 15vh; width: 350px;"><div class="el-dialog__header"><span class="el-dialog__title"></span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><!----></div></div></div><!----><!----><!----><div class="app-wrap new-left-nav--wide"><div><div aegis-ignore-first-screen-timing="" class="left-tree-v2 left-tree-v2--cloud left-tree-wide left-tree--dark"><div id="left-tree__logo" class="left-tree__logo left-tree__logo--without-wedev-logo"><a href="https://www.tapd.cn/" class="tapd-logo for-wide"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" class="wide-logo" alt="需求文档图片"></a><a href="https://www.tapd.cn/" class="tapd-logo for-narrow"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" class="narrow-logo" alt="需求文档图片"></a><!----><span class="left-tree__logo--right switch-logo--dark switch-logo--wide"><i class="el-tooltip tree-switch tapd-icon-arrow-left-v2 clickable" aria-describedby="el-tooltip-8972" tabindex="0"></i></span></div><div class="left-tree__wrap webkit-scrollbar" style="overflow: hidden;"><div id="left-tree-top" class="left-tree-top for-wide"><div class="search left-tree-mid"><div class="left-tree-top__search"><input type="text"><i class="tapd-icon-search-v3 not-hover"></i><i class="not-hover"></i></div><div class="search__panel webkit-scrollbar"><!----><!----><div class="webkit-scrollbar" style="display: none;"><div class="search__workspace" style="display: none;"><div class="left-tree-mid__title"><span><span>协作空间</span></span></div><ul class="content__list"><a style="display: none;"><li class="load-more unimportant-word"><span class="secondary_title">加载更多</span><i class="tapd-icon-new-icon-pulldown not-hover"></i></li></a></ul></div><div class="search__global" style="display: none;"><div class="left-tree-mid__title"><span><span>工作项</span></span></div><ul class="content__list"><a style="display: none;"><li class="load-more unimportant-word"><span class="secondary_title">加载更多</span><i class="tapd-icon-new-icon-pulldown not-hover"></i></li></a></ul></div></div></div></div><!----><div class="left-tree-top__myspace"><a normal-link="" href="https://www.tapd.cn/my_dashboard"><div class="left-tree-top__myspace-item"><i class="tapd-icon-nav-home not-hover"></i><span>主页</span></div></a><a fe-link="" link="/my/work" href="https://www.tapd.cn/tapd_fe/my/work"><div class="left-tree-top__myspace-item"><i class="tapd-icon-nav-todo not-hover"></i><span>我的工作</span></div></a><a fe-link="" link="/20852831/knlibrary" href="https://www.tapd.cn/tapd_fe/20852831/knlibrary"><div class="left-tree-top__myspace-item" style="display: none;"><i class="tapd-icon-nav-todo not-hover"></i><span>知识库</span></div></a><a href="https://www.tapd.cn/personal_documents/my_create_personal/?from=left_tree_v2"><div class="left-tree-top__myspace-item"><i class="tapd-icon-nav-document not-hover"></i><span>我的文档</span></div></a><a href="https://www.tapd.cn/tapd_fe/20852831/knowledge" style="display: none;"><div class="left-tree-top__myspace-item"><i class="tapd-icon-yunji-v2 not-hover"></i><span>知识库</span></div></a><a fe-link="" link="/worktable/search" href="https://www.tapd.cn/tapd_fe/worktable/search"><div class="left-tree-top__myspace-item"><i class="tapd-icon-nav-filter not-hover"></i><span>查询过滤</span></div></a></div></div><div class="left-tree-top for-narrow"><div class="left-tree-top__myspace-item left-tree-top__search"><a><i class="tapd-icon-search-v3 not-hover"></i></a></div><div class="left-tree-top__myspacew"><div class="left-tree-top__myspace-item"><a href="https://www.tapd.cn/my_dashboard"><i class="tapd-icon-home-v2 not-hover"></i> 主页 </a></div><div class="left-tree-top__myspace-item"><a fe-link="" link="/my/work" href="https://www.tapd.cn/tapd_fe/my/work"><i class="tapd-icon-nav-todo not-hover"></i><span>工作</span></a></div><div class="left-tree-top__myspace-item" style="display: none;"><a href="https://www.tapd.cn/tapd_fe/20852831/knlibrary"><div><i class="tapd-icon-nav-document not-hover" style="display: block;"></i> 知识库 </div></a></div><div class="left-tree-top__myspace-item"><a href="https://www.tapd.cn/personal_documents/my_create_personal/?from=left_tree_v2"><div><i class="tapd-icon-nav-document not-hover" style="display: block;"></i> 文档 </div></a></div><div class="left-tree-top__myspace-item" style="display: none;"><a fe-link="" link="/20852831/knowledge" href="https://www.tapd.cn/tapd_fe/20852831/knowledge"><i class="tapd-icon-yunji-v2 not-hover"></i><span>知识库</span></a></div><div class="left-tree-top__myspace-item"><a fe-link="" link="/worktable/search" href="https://www.tapd.cn/tapd_fe/worktable/search"><div><i class="tapd-icon-nav-filter not-hover" style="display: block;"></i> 过滤 </div></a></div></div></div><div class="left-tree-mid"><!----><div class="left-tree-mid__content"><div class="content__projects"><div id="project-title" class="content__title unimportant-word"><div><span class="secondary_title" style="padding-left: 8px;">最近访问空间</span></div><div class="agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-541" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover create-project-popover" tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><button type="button" class="create-project-btn not-hover agi-button agi-button--text agi-button--level-secondary agi-button--size-medium agi-button--icon-only el-popover__reference" aria-describedby="el-popover-541" tabindex="0"><i class="agi-icon tapd-icon-add-v2 agi-button__icon agi-button__left-icon"></i></button></span></span></div></div><div class="content__adjust-start"></div><ul class="content__list for-wide" style="max-height: 504px;"><li class="current content__list-items"><a fe-link="" link="/22012671/story/list" href="https://www.tapd.cn/tapd_fe/22012671/story/list" title="精诚合作（Web）" origin-link="https://www.tapd.cn/tapd_fe/22012671/story/list" class="content-list-left"><div class="left__logo"><div class="left__logo--rect" style="background-color: rgb(254, 102, 168);"></div></div><span class="left-project-name">精诚合作（Web）</span></a><i class="tapd-icon-star-v2"></i></li><li class="content__list-items"><a fe-link="" link="/43702532/bug/list" href="https://www.tapd.cn/tapd_fe/43702532/bug/list" title="专注用户（APP）" origin-link="https://www.tapd.cn/tapd_fe/43702532/bug/list" class="content-list-left"><div class="left__logo"><div class="left__logo--rect" style="background-color: rgb(40, 171, 128);"></div></div><span class="left-project-name">专注用户（APP）</span></a><i class="tapd-icon-star-v2"></i></li><li class="content__list-items"><a fe-link="" link="/36983849/story/list" href="https://www.tapd.cn/tapd_fe/36983849/story/list" title="后端服务" origin-link="https://www.tapd.cn/tapd_fe/36983849/story/list" class="content-list-left"><div class="left__logo"><div class="left__logo--rect" style="background-color: rgb(0, 179, 235);"></div></div><span class="left-project-name">后端服务</span></a><i class="tapd-icon-star-v2"></i></li><li class="content__list-items"><a fe-link="" link="/20852841/story/list" href="https://www.tapd.cn/tapd_fe/20852841/story/list" title="敏捷研发管理" origin-link="https://www.tapd.cn/tapd_fe/20852841/story/list" class="content-list-left"><div class="left__logo"><div class="left__logo--rect" style="background-color: rgb(0, 179, 235);"></div></div><span class="left-project-name">敏捷研发管理</span></a><i class="tapd-icon-star-v2"></i></li><li class="content__list-items"><a normal-link="" href="https://www.tapd.cn/20852851" title="轻量团队看板" origin-link="https://www.tapd.cn/20852851" class="content-list-left"><div class="left__logo"><div class="left__logo--rect" style="background-color: rgb(247, 193, 0);"></div></div><span class="left-project-name">轻量团队看板</span></a><i class="tapd-icon-star-v2"></i></li><a><li class="load-more unimportant-word"><span class="secondary_title">更多</span><i class="tapd-icon-arrow-right-v3 not-hover secondary_title"></i></li></a><!----></ul></div></div></div></div><div class="left-tree-bottom"><!----><div class="left-tree-bottom__personal"><!----><div><div class="avatar avatar-nav" title="朱海华"><i title="朱海华" class="" style="font-style: normal;"><span class="avatar__text"> 海华 </span></i><!----></div></div><div class="left-tree-bottom__personal--icons"><!----><a href="https://www.tapd.cn/letters/?from=top_nav_worktable_v2" class="el-tooltip icon-notification" aria-describedby="el-tooltip-3709" tabindex="0"><span class="bell-dot">180</span><i class="tapd-icon-bell-v2"></i></a><!----><!----><!----><div><div class="help-list-ref agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-8059" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover help-list-drop-down-popover" tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><span class="agi-popover__reference el-popover__reference" aria-describedby="el-popover-8059" tabindex="0"><div role="tooltip" id="el-popover-6542" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-popover--show-arrow quick-guide-help-popver" tabindex="0" style="display: none;"><!----><div class="agi-popover__content"><div class="agi-popover__body"><div class="agi-popover__content-text"><div class="quick-guide-tooltip"><div><span class="quick-guide-tooltip-text"> 你可以在这里重新打开“快速上手TAPD” </span></div><div class="ops-wrapper"><button type="button" class="quick-guide--tooltip-button agi-button agi-button--plain agi-button--level-primary agi-button--size-small agi-button--text-only"><span class="agi-button__text"> 我已知晓 </span></button></div></div></div></div></div></div><span class="el-popover__reference-wrapper"><a class="el-popover__reference" aria-describedby="el-popover-6542" tabindex="0" style="display: inline-block; position: relative;"><i class="tapd-icon-question-v2 clickable"></i><span class="question-dot"></span></a></span></span></span></span></div></div><!----></div></div></div><div><!----></div><div><!----></div><div class="items-dialog space-dialog global-search-dialog" style="top: 0px; left: 60px; display: none; height: 914px;"><div class="search left-tree-mid"><div class="left-tree-top__search"><input type="text"><i class="tapd-icon-search-v3 not-hover"></i><i class="not-hover"></i></div><div class="search__panel webkit-scrollbar"><!----><!----><div class="webkit-scrollbar" style="display: none;"><div class="search__workspace" style="display: none;"><div class="left-tree-mid__title"><span><span>协作空间</span></span></div><ul class="content__list"><a style="display: none;"><li class="load-more unimportant-word"><span class="secondary_title">加载更多</span><i class="tapd-icon-new-icon-pulldown not-hover"></i></li></a></ul></div><div class="search__global" style="display: none;"><div class="left-tree-mid__title"><span><span>工作项</span></span></div><ul class="content__list"><a style="display: none;"><li class="load-more unimportant-word"><span class="secondary_title">加载更多</span><i class="tapd-icon-new-icon-pulldown not-hover"></i></li></a></ul></div></div></div></div></div><!----><!----><!----><div class="el-dialog__wrapper" style="display: none;"><div role="dialog" aria-modal="true" aria-label="创建项目" class="el-dialog customfield" style="margin-top: 15vh; width: 800px;"><div class="el-dialog__header" style="cursor: grabbing;"><span class="el-dialog__title">创建项目</span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><!----></div></div><!----><!----><!----><!----><!----></div><!----></div><div class="app"><div class="navigation-wrap"><div class="navigation clearfix project-nav navigation-v2 navigation-left-nav-v2"><div class="current-project-area"><div><span title="精诚合作（Web）" class="current-project textoverflow brick clearfix"><div data-v-5e487625="" class="workspace-logo"><i data-v-5e487625="" class="project-logo project-logo-upload"><img data-v-5e487625="" data-src="https://file.tapd.cn/22012671/files/get_logo/logo_22012671_1e1d991e5d7eb6690265.jpg" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" lazy="loaded" alt="需求文档图片"></i><span data-v-5e487625="" class="project-name">精诚合作（Web）</span><!----></div><!----></span></div></div><div><span class="agi-popover__reference"><div role="tooltip" id="el-popover-6395" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-popover--show-arrow project-guide-tooltip" tabindex="0" style="display: none;"><!----><div class="agi-popover__content"><div class="agi-popover__body"><div class="agi-popover__content-text"><div><p class="content">  </p><div class="button-container"><button type="button" class="inform-btn agi-button agi-button--plain agi-button--level-primary agi-button--size-small agi-button--text-only"><span class="agi-button__text"> 我已知晓 </span></button></div></div></div></div></div></div><span class="el-popover__reference-wrapper"><div class="right-icon agi-dropdown el-popover__reference" aria-describedby="el-popover-6395" tabindex="0"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-6036" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover " tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><button type="button" class="agi-button agi-button--text agi-button--level-secondary agi-button--size-small agi-button--icon-only el-popover__reference" aria-describedby="el-popover-6036" tabindex="0"><i class="agi-icon tapd-icon-more-v2 agi-button__icon agi-button__left-icon"></i></button></span></span></div></span></span><div class="right-icon add-user j-no-hook"><!----><!----></div><div class="right-icon"><!----><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAeCAYAAABqpJ3BAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMWSURBVHgB1ZgxTBRBFIb/QQqhQugEEozGSiMWmmghBAtNJFEbEyuNhWAsWCg1RsFICdcYiY1YmVhBogWFRmksLMBoZUwkAa0Ur7rrbpz/Zp67t+7sLXKG249MdnZ2bua9ef97d6xCCjoIOlDCGBQGzW2fa/8fjaLZcxUVtYBdelHNFdZ8U1Xi50eDPrPIE9MdRHMwbyydTHKkJT6gR8bHUMEKmsd4ctUc6Iq+HgTxBzURMCd/10y8h2bG2KceFybl9o8D1ZOHLiAPaIwbJ6q2Vh2oap6yUehAPmCSH2VO2ByoGNnkx3jS4YoMlKs4X5FH2rCn1Zz+hcRiOjdrrzMPgc9f/M9Hx2vvfci8mWmgvQ2YuAWUynasqxN4cMf2b98Hfm46A8282Wk7j/PjlBC0mEJ6HmlcuYyGsumM6+wMxw7uD/tHDoX93r32uvEtcSlz8AOMQH/y15mDpzN0Cni9jEzISftYN8b0dFvjxLATx8Pn/YfDvRgBIpH6m76WusnLDw+ftWFvBCKPrmgEDoR70TnZq7fbXn0RqDpQj5dLdsFzZ9AQ4g6IfJhnNJR79TjDxYH1797l6jvw6q3d9PRA7an9KxvOGDFS5PPuPbD60fb7XR6IhMpeCaEVWXj6zFSBmzahWZXS8FUjyY0f8Qg4+Uilu3TRJLLJg+cLoZN+CWWIgCzOxs1kQx9aJzeBp0mti1ToCCMcbRyTXCiV05I4YwQIo8BazSiwVvu4MYG6sJS2GwNPHrP30e8ZyohyHXY5l3L6JFsECE+G+SBldTusO6OGBuyV+hc+fLLXaGVKIbsD5MVSWFa3g1QiIRoBOicSIw2LAKF+paz6UCq5RYk6EP+Zwj2iRqeUUEMxew4IlBEl5Cupj2aSx6Pf0BsRo6LyEZgHIqFyqoRW/RGIV48oTOj4c1/1SVqHpVTGk34oMg/keZqENBaVHgk08orCvq3lQHMxH/5Hljc0fvE1C7t5jcCUvCPaehXaWbT5m5I3EiQ/DlA2qDWe5MEBVsk3RuzXkl4ttroJzcZatSksYzcKqlAo+ib+BhqtNlhiT6vQAAAAAElFTkSuQmCC" class="tip_new" style="display: none;"><!----></div><div class="main-search-area nav-v2"><div class="main-search brick"><form class="el-form" method="get" accept-charset="UTF-8"><div class="dropdown"><input type="text" autocomplete="off" placeholder="搜索标题或ID" class="input"><div class="dropdown-list" style="display: none;"><p class="item-title recently-search-title"> 最近访问 </p><ul class="search-result-list"><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(53, 130, 251);">S</span><a href="https://www.tapd.cn/22012671/prong/stories/view/1122012671001005197" class="item-name">【血压项目】血压公卫表的权限修改</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(53, 130, 251);">S</span><a href="https://www.tapd.cn/22012671/prong/stories/view/1122012671001005168" class="item-name">【数据导出】服务包证据链增加内容</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(53, 130, 251);">S</span><a href="https://www.tapd.cn/22012671/prong/stories/view/1122012671001005124" class="item-name">【P0】万寿路血压项目病历优化</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(248, 94, 94);">B</span><a href="https://www.tapd.cn/22012671/bugtrace/bugs/view?bug_id=1122012671001007365" class="item-name">【数据工作台】【服务收入数据】选择日期范围，请求报错</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(53, 130, 251);">S</span><a href="https://www.tapd.cn/22012671/prong/stories/view/1122012671001003497" class="item-name">【共同照护web(新增)】批量导出病例功能</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(248, 94, 94);">B</span><a href="https://www.tapd.cn/22012671/bugtrace/bugs/view?bug_id=1122012671001007342" class="item-name">【平台运营工作台】【商品管理】重复打开成本价格窗口并点击确定按钮，会生成多个相同的历史成本价</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(248, 94, 94);">B</span><a href="https://www.tapd.cn/22012671/bugtrace/bugs/view?bug_id=1122012671001007352" class="item-name">【平台运营工作台】【商品规格编码管理】上传文件的二次成本价为负数或非数字时，未对其进行无效参数处理</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(248, 94, 94);">B</span><a href="https://www.tapd.cn/22012671/bugtrace/bugs/view?bug_id=1122012671001007353" class="item-name">【平台运营工作台】【商品规格编码管理】导入只有一条数据异常数据的文件，错误提示展示内容不正确</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(248, 94, 94);">B</span><a href="https://www.tapd.cn/22012671/bugtrace/bugs/view?bug_id=1122012671001007357" class="item-name">【平台运营工作台】【商品详情】没有成本价的旧商品，添加成本价后，无法保存</a></li><li class="item-li"><span class="workitem-icon workitem-icon__mini item-img" style="background-color: rgb(248, 94, 94);">B</span><a href="https://www.tapd.cn/22012671/bugtrace/bugs/view?bug_id=1122012671001007359" class="item-name">【平台运营工作台】【商品管理】新增商品时填写成本价，成本价保存失败</a></li></ul></div><div class="dropdown-list result-list" style="display: none;"><ul><li class=""><!----><ul class="item-list" style="display: none;"></ul><!----></li><li class=""><!----><ul class="item-list" style="display: none;"></ul><!----></li><li class=""><!----><ul class="item-list" style="display: none;"></ul><!----></li><li class=""><!----><ul class="item-list" style="display: none;"></ul><!----></li><li class=""><!----><ul class="item-list" style="display: none;"></ul><!----></li><li class=""><!----><ul class="item-list" style="display: none;"></ul><!----></li><li class=""><!----><ul class="item-list" style="display: none;"></ul><!----></li><li class="empty-item search-result-list"><img src="data:image/png;base64,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"><p>抱歉，未搜索到内容</p></li><li class="search-entry-list"><a type="tapd" target="_blank">在&nbsp;<b>TAPD</b>&nbsp;中搜索</a><i class="tapd-icon-arrow-right-v2"></i></li></ul></div></div><a tabindex="0" class="submit"><i class="agi-icon tapd-icon-search-v2"></i></a><!----></form></div></div><!----></div><div class="nav-main-wrap"><div class="dropdown-quick-add agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-5603" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover quick-add-menu" tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><a class="dropdown-toggle el-popover__reference" aria-describedby="el-popover-5603" tabindex="0"><button type="button" class="agi-button agi-button--text agi-button--level-secondary agi-button--size-medium agi-button--icon-only"><i class="agi-icon tapd-icon-add-v2 agi-button__icon agi-button__left-icon"></i></button></a></span></span></div><ul class="nav-main brick" style="width: 491.026px;"><li name="需求" class="brick"><a fe-link="" link="/22012671/story/list" href="https://www.tapd.cn/tapd_fe/22012671/story/list">需求</a></li><li name="迭代" class="brick"><a normal-link="" href="https://www.tapd.cn/22012671/prong/iterations">迭代</a></li><li name="故事墙" class="brick"><a fe-link="" link="/22012671/storywall" href="https://www.tapd.cn/tapd_fe/22012671/storywall">故事墙</a></li><li name="缺陷" class="brick"><a fe-link="" link="/22012671/bug/list" href="https://www.tapd.cn/tapd_fe/22012671/bug/list">缺陷</a></li><li name="文档" class="brick"><a normal-link="" href="https://www.tapd.cn/22012671/documents/file_list">文档</a></li><li name="报表" class="brick"><a normal-link="" href="https://www.tapd.cn/22012671/report/reports/index">报表</a></li><li name="看板" class="brick"><a normal-link="" href="https://www.tapd.cn/22012671/board/board">看板</a></li></ul><div class="agi-dropdown dropdown fe-dropdown-nav-more brick" name="更多"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-1007" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover more-menu-dropdown" tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><a href="javascript:void(0)" class="dropdown-toggle el-popover__reference" aria-describedby="el-popover-1007" tabindex="0"><span class="text">更多</span><i class="agi-icon tapd-icon-arrow-down-v2"></i></a></span></span></div></div></div><div class="project-nav-helper"></div><!----><div class="el-dialog__wrapper announcement-edit" style="display: none;"><div role="dialog" aria-modal="true" aria-label="新建公告" class="el-dialog" style="margin-top: 15vh; width: 600px;"><div class="el-dialog__header"><span class="el-dialog__title">新建公告</span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><div class="el-dialog__footer"><span class="dialog-footer"><button type="button" class="el-button custom-btn custom-btn-default el-button--primary"><!----><!----><span>预览 </span></button><button type="button" class="el-button custom-btn custom-btn-default el-button--primary"><!----><!----><span>提交</span></button><button type="button" class="el-button custom-btn custom-btn-cancel el-button--default"><!----><!----><span>取消</span></button></span></div></div></div><!----><!----><!----></div><!----><div element-loading-spinner="tapd-loading" class="app__content webkit-scrollbar"><div class="story-wrap"><div class="story-detail"><div class="detail-container-outer-wrapper detail-container-ourter detail-container-outer-wrapper--page"><div class="detail-container-header-top detail-container-header-top--normal detail-container-header-top--with-title"><div class="status-transfer-wrap status-transfer-wrap--large"><div class="status-label-button"><button type="button" class="el-button el-button--default capsule capsule--end" title="DONE"><!----><!----><span><span class="capsule__text"> DONE </span><i class="tapd-icon-arrow-down-v2"></i></span></button></div><div special="true"><!----></div></div><div class="detail-container-header-top__info-wrapper"><div data-v-b2059c10="" class="detail-container-header"><div data-v-b2059c10="" class="detail-container-header__workitem"><button data-v-b2059c10="" type="button" class="agi-button agi-button--text agi-button--level-secondary agi-button--size-small agi-button--text-only"><span class="agi-button__text"><span data-v-b2059c10="" class="workitem-icon" style="background-color: rgb(53, 130, 251);">STORY</span></span></button></div><!----><div data-v-b2059c10="" class="title-area"><div data-v-b2059c10="" class="title-wrap editable-text"><span data-v-562ee870="" data-v-b2059c10="" class="agi-popover__reference" style="width: 100%;"><div role="tooltip" id="el-popover-9567" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-popover--show-arrow" tabindex="0" style="width: 300px; display: none;"><!----><div class="agi-popover__content"><div class="agi-popover__body"><div class="agi-popover__content-text"><div data-v-562ee870="" class="preview"><div data-v-562ee870="" class="preview__content webkit-scrollbar">0</div></div></div></div></div></div><span class="el-popover__reference-wrapper"><div data-v-b2059c10="" class="tapd-inline-input el-popover__reference" aria-describedby="el-popover-9567" tabindex="0"><div title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div title="【共同照护web(新增)】批量导出病例功能" class="tapd-inline-label-selectable"><p class="label-selectable__tag"> 【共同照护web(新增)】批量导出病例功能 <!----></p></div></div></div><!----><!----></div></div></span></span></div><div data-v-b2059c10="" class="action-wrap"><!----><button data-v-b2059c10="" type="button" class="title-area__follow-button agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-star-v2 agi-button__icon agi-button__left-icon"></i></button><div data-v-b2059c10="" class="title-area__copy-link-button agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-8440" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover " tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><button data-v-b2059c10="" type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only el-popover__reference" aria-describedby="el-popover-8440" tabindex="0"><i class="agi-icon tapd-icon-link-v2 agi-button__icon agi-button__left-icon"></i></button></span></span></div></div><div data-v-b2059c10="" class="title-area__id"> ID: 1003497 </div></div><div data-v-b2059c10="" class="el-dialog__wrapper" width="600px" opmenudisabledconfig="[object Object]" workitemtypeslist="[object Object]" showentityid="true" currentworktypeitem="[object Object]" detaildata="[object Object]" menuworkitemtypeid="" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog agi-dialog agi-dialog--small " style="margin-top: 15vh; width: 600px;"><div class="el-dialog__header"><div class="agi-dialog__header"><span class="agi-dialog__title">新建复制链接格式</span><i class="agi-icon tapd-icon-close-v2 agi-dialog__close-icon"></i></div><!----></div><!----><div class="el-dialog__footer"><div class="agi-dialog__footer"><button type="button" class="agi-button agi-button--default agi-button--level-primary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">确认</span></button><button type="button" class="agi-button agi-button--plain agi-button--level-secondary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">取消</span></button></div></div></div></div><!----></div><!----></div><!----><div data-v-b2059c10="" class="detail-container-header-right"><div data-v-b2059c10="" class="right-option"><i data-v-b2059c10="" class="tapd-icon-edit-v2"></i><!----><!----><!----><div data-v-b2059c10="" class="more"><div data-v-b2059c10="" class="header-more-operation__wrap"><div class="agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-204" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover " tabindex="0" style="display: none;"><!----><div class="agi-popover__content"><div class="agi-popover__body"><div class="agi-popover__content-text"><div class="agi-dropdown-menu agi-dropdown-menu--size-small agi-scrollbar" style="max-height: 556px;"><div style="min-width: 188px;"><li class="agi-dropdown-menu-item agi-dropdown-menu-item--disabled"><i class="agi-icon tapd-icon-sub-workitem-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 创建子需求 </span></li><!----></div><div style="min-width: 188px;"><li class="agi-dropdown-menu-item"><i class="agi-icon tapd-icon-bug-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 创建缺陷 </span></li><!----></div><div style="min-width: 188px;"><div class="agi-dropdown" icon="tapd-icon-link-v2" style="display: inline;"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-4890" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover " tabindex="0" style="display: none;"><!----><div class="agi-popover__content"><div class="agi-popover__body"><div class="agi-popover__content-text"><div class="agi-dropdown-menu agi-dropdown-menu--size-small agi-scrollbar" style="max-height: 460px;"><div><li class="agi-dropdown-menu-item"><i class="agi-icon tapd-icon-workitems-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 需求 </span></li><!----><li class="agi-dropdown-menu-item"><i class="agi-icon tapd-icon-bug-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 缺陷 </span></li><!----><li class="agi-dropdown-menu-item"><i class="agi-icon tapd-icon-relationship-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 添加前置对象 </span></li><!----><li class="agi-dropdown-menu-item"><i class="agi-icon tapd-icon-relationship-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 添加后置对象 </span></li><!----></div></div></div></div></div></div><span class="el-popover__reference-wrapper"><li class="agi-dropdown-menu-custom-item agi-dropdown-sub-menu el-popover__reference" aria-describedby="el-popover-4890" tabindex="0" style="background-color: transparent;"><i class="agi-icon tapd-icon-link-v2 agi-dropdown-sub-menu__icon"></i> 关联 <span class="agi-dropdown-sub-menu__indicator"><i class="agi-icon tapd-icon-arrow-right-v2"></i></span></li></span></span></div><div class="agi-dropdown-menu-divider"></div></div><div style="min-width: 188px;"><li class="agi-dropdown-menu-item"><i class="agi-icon tapd-icon-copy-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 复制需求 </span></li><!----></div><div style="min-width: 188px;"><li class="agi-dropdown-menu-item agi-dropdown-menu-item--disabled"><i class="agi-icon tapd-icon-move-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 移动需求 </span></li><!----></div><div style="min-width: 188px;"><li class="agi-dropdown-menu-item agi-dropdown-menu-item--disabled"><i class="agi-icon tapd-icon-bug-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 转缺陷 </span></li><!----></div><div style="min-width: 188px;"><li class="agi-dropdown-menu-item"><i class="agi-icon tapd-icon-print-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 打印需求（导出 PDF） </span></li><div class="agi-dropdown-menu-divider"></div></div><div style="min-width: 188px;"><li class="agi-dropdown-menu-item agi-dropdown-menu-item--disabled"><i class="agi-icon tapd-icon-archive-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 归档 </span></li><!----></div><div style="min-width: 188px;"><li class="agi-dropdown-menu-item agi-dropdown-menu-item--dangerous"><i class="agi-icon tapd-icon-trash-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text"> 删除 </span></li><!----></div></div></div></div></div></div><span class="el-popover__reference-wrapper"><button type="button" class="agi-button agi-button--text agi-button--level-secondary agi-button--size-small agi-button--icon-only el-popover__reference" aria-describedby="el-popover-204" tabindex="0"><i class="agi-icon tapd-icon-more-v2 agi-button__icon agi-button__left-icon"></i></button></span></span></div></div></div><!----><!----></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><div data-v-b2059c10="" class="el-dialog__wrapper dependency-relations-dialog" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog" style="margin-top: 15vh; width: 800px;"><div class="el-dialog__header"><span style="font-size: 14px; color: rgb(24, 43, 80);">依赖关系</span><!----><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><!----></div></div><!----></div></div><!----><div class="detail-container-wrapper entity-detail-layout webkit-scrollbar"><!----><div class="detail-container"><div class="detail-container-left webkit-scrollbar"><div class="view-assitant-review-result__btn-wrapper" style="display: none;"><div class="view-assitant-review-result__btn" style="height: 125px;"><div class="view-assitant-review-result__btn-icon"></div><div class="view-assitant-review-result__btn-text" style="height: 85px;"><span class=""> AI评审结果 </span></div></div></div><div class="detail-container-left__scroll-wrapper webkit-scrollbar"><div data-v-b2059c10="" class="detail-container-header" app-id=""><div data-v-b2059c10="" class="detail-container-header__workitem"><button data-v-b2059c10="" type="button" class="agi-button agi-button--text agi-button--level-secondary agi-button--size-small agi-button--text-only"><span class="agi-button__text"><span data-v-b2059c10="" class="workitem-icon" style="background-color: rgb(53, 130, 251);">STORY</span></span></button></div><!----><div data-v-b2059c10="" class="title-area"><div data-v-b2059c10="" class="title-wrap editable-text"><span data-v-562ee870="" data-v-b2059c10="" class="agi-popover__reference" style="width: 100%;"><div role="tooltip" id="el-popover-3746" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-popover--show-arrow" tabindex="0" style="width: 300px; display: none;"><!----><div class="agi-popover__content"><div class="agi-popover__body"><div class="agi-popover__content-text"><div data-v-562ee870="" class="preview"><div data-v-562ee870="" class="preview__content webkit-scrollbar">0</div></div></div></div></div></div><span class="el-popover__reference-wrapper"><div data-v-b2059c10="" class="tapd-inline-input el-popover__reference" aria-describedby="el-popover-3746" tabindex="0"><div title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div title="【共同照护web(新增)】批量导出病例功能" class="tapd-inline-label-selectable"><p class="label-selectable__tag"> 【共同照护web(新增)】批量导出病例功能 <!----></p></div></div></div><!----><!----></div></div></span></span></div><div data-v-b2059c10="" class="action-wrap"><!----><button data-v-b2059c10="" type="button" class="title-area__follow-button agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-star-v2 agi-button__icon agi-button__left-icon"></i></button><div data-v-b2059c10="" class="title-area__copy-link-button agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-5082" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover " tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><button data-v-b2059c10="" type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only el-popover__reference" aria-describedby="el-popover-5082" tabindex="0"><i class="agi-icon tapd-icon-link-v2 agi-button__icon agi-button__left-icon"></i></button></span></span></div></div><!----></div><div data-v-b2059c10="" class="el-dialog__wrapper" width="600px" opmenudisabledconfig="[object Object]" workitemtypeslist="[object Object]" currentworktypeitem="[object Object]" detaildata="[object Object]" menuworkitemtypeid="0" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog agi-dialog agi-dialog--small " style="margin-top: 15vh; width: 600px;"><div class="el-dialog__header"><div class="agi-dialog__header"><span class="agi-dialog__title">新建复制链接格式</span><i class="agi-icon tapd-icon-close-v2 agi-dialog__close-icon"></i></div><!----></div><!----><div class="el-dialog__footer"><div class="agi-dialog__footer"><button type="button" class="agi-button agi-button--default agi-button--level-primary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">确认</span></button><button type="button" class="agi-button agi-button--plain agi-button--level-secondary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">取消</span></button></div></div></div></div><!----></div><!----><!----><div data-v-82484c12="" class="detail-container-content detail-container-content"><ul data-v-82484c12="" class="tab-container"><div data-v-82484c12="" class="tab-container-wrapper"><li data-v-82484c12="" class="tab-container-item current-tab"><a data-v-82484c12="" class="container-item-link"><span data-v-82484c12="" class="tab-name">详细信息</span><!----></a></li><li data-v-82484c12="" id="SubStories" class="tab-container-item"><a data-v-82484c12="" class="container-item-link"><span data-v-82484c12="" class="tab-name">子需求</span><label data-v-82484c12="">(0)</label></a></li><li data-v-82484c12="" id="Bugs" class="tab-container-item"><a data-v-82484c12="" class="container-item-link"><span data-v-82484c12="" class="tab-name">缺陷 </span><label data-v-82484c12="">(7)</label></a></li><li data-v-82484c12="" id="Revisions" class="tab-container-item"><a data-v-82484c12="" class="container-item-link"><span data-v-82484c12="" class="tab-name"> 变更历史</span><label data-v-82484c12="">(48)</label></a></li><!----><!----><!----><!----><li data-v-82484c12="" class="tab-container-item"><div data-v-82484c12="" class="more-item-wrap agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-65" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover " tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><a data-v-82484c12="" class="container-item-link more-item-trigger el-popover__reference" aria-describedby="el-popover-65" tabindex="0"><span data-v-82484c12="" class="tab-name">更多</span><!----><i data-v-82484c12="" class="tapd-icon-arrow-down-v2"></i></a></span></span></div></li></div></ul><!----><!----><div data-v-82484c12="" class="tab-item tab-item__related-bug" entityid="1122012671001003497" entitytype="story" customeditortoolbar="" workspaceid="22012671" detaildata="[object Object]" parentinfo="[object Object]" parentstories="[object Object]" mainlocation="/prong/stories/stories_list" editstateusers="" sockettoken="ce815458fb943cb1b14e7c6c005bade156a0fc33" socketroomid="WORKITEMS_CHANGE_story_detail_1122012671001003497" fromiterationid="" style="display: none;"><div class="tab-item-header"><div class="fl"><div class="operate-item"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-mini"><i class="agi-icon tapd-icon-add-v2 agi-button__icon agi-button__left-icon"></i><span class="agi-button__text"> 快速添加缺陷 </span></button></div></div><div class="fl"><div class="operate-item"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-mini"><i class="agi-icon tapd-icon-search-v2 agi-button__icon agi-button__left-icon"></i><span class="agi-button__text"> 关联缺陷 </span></button></div></div><div class="fr"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-mini agi-button--icon-only"><i class="agi-icon tapd-icon-setting-v2 agi-button__icon agi-button__left-icon"></i></button></div></div><div class="tab-item-body"><div class="tapd-light-table tapd-scrollbar tapd-light-table0 tapd-light-table-small tapd-light-table-upgrade without_drag" element-loading-spinner="tapd-loading"><div class="tapd-table__head-wrap tapd-table__head-wrap--hide-scrollbar tapd-table__wrap--fixed border-left-shadow"><div class="tapd-table__head" style="pointer-events: unset;"><table cellspacing="0" cellpadding="0" border="0" class="tapd-table__head--adjustable" style="height: 40px; line-height: 40px;"><colgroup><col class="col-widget--menu" style="width: 52px;"><col class="col-field--id" style="width: 120px;"><col class="col-field--title" style="width: 607px;"><col class="col-field--priority" style="width: 120px;"><col class="col-field--severity" style="width: 120px;"><col class="col-field--status" style="width: 120px;"><col class="col-field--iteration_id" style="width: 120px;"><col class="col-field--current_owner" style="width: 120px;"><col class="col-field--reporter" style="width: 120px;"><col class="col-field--_operation" style="width: 120px;"><col width="10"></colgroup><thead><tr><th widget="menu" class="tapd-light-table__head-cell--fixed-right" style="left: 0px;"><div class="row-head-cell row-cell--widget" style="--height: 40px;"><div class="table-widget__menu"><i class="tapd-icon-more-v2" style="display: none;"></i></div></div></th><th theme="miniProject" field="id" class=""><div class="row-head-cell"><div class="row-head-cell__hover row-head-cell__hover--flex4"><div class="row-head-cell__content enable-sortable"><div title="ID" class="tapd-table__text-label"> ID </div></div><div class="row-head-cell__suffix"><i class="tapd-icon-sort-v2"></i></div></div><span class="row-head-cell__more"><div class="agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-6548" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover " tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><button type="button" class="agi-button agi-button--text agi-button--level-secondary agi-button--size-small agi-button--icon-only el-popover__reference" aria-describedby="el-popover-6548" tabindex="0"><i class="agi-icon tapd-icon-more-v2 agi-button__icon agi-button__left-icon"></i></button></span></span></div></span></div><span class="row-head__split-line"></span></th><th theme="miniProject" field="title" class=""><div class="row-head-cell"><div class="row-head-cell__hover"><div class="row-head-cell__content enable-sortable"><div class="row-head-cell__name"><span class="cell__name-icon" style="display: none;"><span><i class="tapd-icon-triangle-down-v2"></i></span></span><span class="cell__name"><span title="标题" class="cell__name-label"> 标题 </span><span class="cell__name-count">(7)</span><div class="row-head-cell__hover__bg"></div></span></div></div><div class="row-head-cell__suffix"><i class="tapd-icon-sort-v2"></i></div></div></div><span class="row-head__split-line"></span></th><th theme="miniProject" field="priority" class=""><div class="row-head-cell"><div class="row-head-cell__hover"><div class="row-head-cell__content enable-sortable"><div title="优先级" class="tapd-table__text-label"> 优先级 </div></div><div class="row-head-cell__suffix"><i class="tapd-icon-sort-v2"></i></div></div></div><span class="row-head__split-line"></span></th><th theme="miniProject" field="severity" class=""><div class="row-head-cell"><div class="row-head-cell__hover"><div class="row-head-cell__content enable-sortable"><div title="严重程度" class="tapd-table__text-label"> 严重程度 </div></div><div class="row-head-cell__suffix"><i class="tapd-icon-sort-v2"></i></div></div></div><span class="row-head__split-line"></span></th><th theme="miniProject" field="status" class=""><div class="row-head-cell"><div class="row-head-cell__hover"><div class="row-head-cell__content enable-sortable"><div title="状态" class="tapd-table__text-label"> 状态 </div></div><div class="row-head-cell__suffix"><i class="tapd-icon-sort-v2"></i></div></div></div><span class="row-head__split-line"></span></th><th theme="miniProject" field="iteration_id" class=""><div class="row-head-cell"><div class="row-head-cell__hover"><div class="row-head-cell__content enable-sortable"><div title="迭代" class="tapd-table__text-label"> 迭代 </div></div><div class="row-head-cell__suffix"><i class="tapd-icon-sort-v2"></i></div></div></div><span class="row-head__split-line"></span></th><th theme="miniProject" field="current_owner" class=""><div class="row-head-cell"><div class="row-head-cell__hover"><div class="row-head-cell__content enable-sortable"><div title="处理人" class="tapd-table__text-label"> 处理人 </div></div><div class="row-head-cell__suffix"><i class="tapd-icon-sort-v2"></i></div></div></div><span class="row-head__split-line"></span></th><th theme="miniProject" field="reporter" class=""><div class="row-head-cell"><div class="row-head-cell__hover"><div class="row-head-cell__content enable-sortable"><div title="创建人" class="tapd-table__text-label"> 创建人 </div></div><div class="row-head-cell__suffix"><i class="tapd-icon-sort-v2"></i></div></div></div><span class="row-head__split-line"></span></th><th theme="miniProject" field="_operation" class="tapd-light-table__head-cell--fixed-right tapd-light-table__head-cell--fixed-right-last" style="right: 8px;"><div class="row-head-cell"><div class="row-head-cell__hover"><div class="row-head-cell__content disable-sortable"><div title="操作" class="tapd-table__text-label"> 操作 </div></div></div></div><span class="row-head__split-line"></span></th><th class="gatter" style="width: 10px;"></th></tr></thead></table></div></div><div class="tapd-grid-edit"><div class="agi-virtual-scroll tapd-table__body-wrap tapd-scrollbar tapd-table__body-wrap--nofoot tapd-table__wrap--fixed border-left-shadow" tableindex="0" style="height: 380px;"><div class="agi-virtual-scroll__wrap agi-virtual-scroll__mac--wrap"><div style="height: 320px;"></div><div id="" class="agi-virtual-scroll__inner" style="margin-top: 0px;"><div class="tapd-table-content"><table cellspacing="0" cellpadding="0" border="0" class=""><colgroup><col class="col-widget--menu" style="width: 52px;"><col class="col-field--id" style="width: 120px;"><col class="col-field--title" style="width: 607px;"><col class="col-field--priority" style="width: 120px;"><col class="col-field--severity" style="width: 120px;"><col class="col-field--status" style="width: 120px;"><col class="col-field--iteration_id" style="width: 120px;"><col class="col-field--current_owner" style="width: 120px;"><col class="col-field--reporter" style="width: 120px;"><col class="col-field--_operation" style="width: 120px;"><col width="10"></colgroup><tbody><tr idx="-1" class="draggable-item row-item row-create-child row-even" style="--height: 40px;"><td widget="menu"></td><td colspan="11"><div class="table-widget__create-child-tips" style="height: 40px; line-height: 40px;"><div class="add-title--wrap"><span class="add-icon-btn--wrap"><i class="tapd-icon-add-v2" style="line-height: 40px;"></i></span>快速添加缺陷</div></div></td></tr><tr idx="1" workspace_id="22012671" id="1122012671001002738" type="bug" hoveridx="1" class="row-item row-odd"><td widget="menu" class="tapd-light-table__head-cell--fixed-right" style="left: 0px;"><div class="row-cell row-cell--widget height-constraint" style="--height: 40px;"><div class="table-widget__menu"><i class="tapd-icon-more-v2"></i></div></div></td><td fieldcomponent="linkDirect" class="td--linkDirect height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell j-preview-jump  " title="1122012671001002738" data-disable-tips="" data-grid-id="1122012671001002738" data-grid-entity-type="bug" data-grid-field="id" data-grid-value="1122012671001002738"> <span class="tapd-grid-edit-link id" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002738" target="_blank" title="1002738" ondragstart="return false" copy-text=""> 1002738 </span></div></div></td><td fieldcomponent="tapd-inline-title-input" class="td--tapd-inline-title-input height-constraint" style="--height: 40px;"><div data-v-0fb95900="" class="tapd-inline-title-input"><div data-v-0fb95900="" title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div data-v-0fb95900="" class="title-input__prefix"><!----><!----><span data-v-0fb95900="" class="workitem-icon" style="background-color: rgb(248, 94, 94);">BUG</span><!----></div><div data-v-0fb95900="" class="title-input__link title-input__link--status-done"><span data-v-0fb95900="" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002738" target="_blank" ondragstart="return false" copy-text="" class="tapd-grid-edit-link title-input__link--clickable"> 【WEB】【数据工作台】批量导出病历，需导出服务包距离服务包购买时间最近的病历 </span><!----></div><div data-v-0fb95900="" class="title-input__suffix"><!----><span data-v-0fb95900="" class="title-icon__item"><a data-v-0fb95900=""><!----></a></span><!----><!----><!----><i data-v-0fb95900="" slot="icon" class="title-icon__editor t2-action-button__left-icon tapd-icon-edit-v2"></i></div><!----><!----></div></div><!----><!----></div></div></td><td fieldcomponent="tapd-inline-priority-select" class="td--tapd-inline-priority-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-priority-label-v2-wrapper tapd-grid-edit-cell--editable " title="中" data-disable-tips="" data-grid-id="1122012671001002738" data-grid-entity-type="bug" data-grid-field="priority" data-grid-edit="tapd-inline-priority-select" data-grid-value="medium"><div class="tapd-priority-label-v2 colorful-labels"><span class="colorful-labels__item" style="color: rgb(255, 255, 255); background-color: rgb(40, 171, 128); border: 1px solid rgba(40, 171, 128, 0.06);"><span class="colorful-labels__item-text">中</span></span></div></div></div></td><td fieldcomponent="tapd-inline-severity-select" class="td--tapd-inline-severity-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell tapd-severity-select__label tapd-grid-edit-cell--severitySelect tapd-grid-edit-cell--editable " title="一般" data-disable-tips="" data-grid-id="1122012671001002738" data-grid-entity-type="bug" data-grid-field="severity" data-grid-edit="tapd-inline-severity-select" data-grid-value="normal"><div class="tapd-severity-select__colorable tapd-severity-select__label--normal">一般</div></div></div></td><td fieldcomponent="workflow" class="td--workflow height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell undefined tapd-grid-edit-cell--workflow tapd-grid-edit-cell--editable " title="已关闭" data-disable-tips="" data-grid-id="1122012671001002738" data-grid-entity-type="bug" data-grid-field="status" data-grid-edit="workflow" data-grid-value="closed"><div type="button" class="el-button el-button--default capsule capsule--end  " value="closed" field="status" title="已关闭" data-disable-tips="" style="null">
                  <span>已关闭</span>
                  </div></div></div></td><td fieldcomponent="tapd-inline-tree-select" class="td--tapd-inline-tree-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="web-2023.7" data-disable-tips="" data-grid-id="1122012671001002738" data-grid-entity-type="bug" data-grid-field="iteration_id" data-grid-html="" data-grid-edit="tapd-inline-tree-select" data-grid-value="web-2023.7"><span class="tapd-grid-edit-link" href="https://www.tapd.cn/22012671/prong/iterations/card_view/1122012671001000196" target="_blank" ondragstart="return false">web-2023.7</span> <i style="display: " class="tapd-icon-arrow-down-v2"></i></div></div></td><td fieldcomponent="tapd-inline-user-select" class="td--tapd-inline-user-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002738" data-grid-entity-type="bug" data-grid-field="current_owner" data-grid-edit="tapd-inline-user-select" data-grid-value="朱海华" title_username=""><div class="avatar-wrapper"><span class="agi-avatar agi-avatar--type-image agi-avatar--size-minimum"><div class="agi-avator-inner" style="background-image:url('https://www.tapd.cn/22012671/users/avatar/朱海华'), url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHSSURBVHgB7ZhNS8NAEIZHDdr6AYq1lii1VEGPHvz1XryLBw8KEdpaao3RVhRabZSI5l0sVKglyWZoN8wDe0mXTZ/MbmYmc6dn59+UYeYp44ig6Yig6Yig6Yig6Yig6Yig6WRe0CJmLMuivd1tskublMstqWu9/rsajaZLA/+DOGEVtEsFOjwoh5ILf66vrS6rUSxsUL15T632I3HBJlgOowa5iTcPxTEnCL7I9brEAcsZzIdbcb+yE3n+uCinBYtgtWLH+sOYi4hzwCKI8xUXnFcOZkYw//uGTRtJ9ElIktt6/TfigEXQ9Z4pLr3+gDhgEWy1PZXbohIEgUr4HLAIQu6m1oo8vx6WbL7/SRywVTKoTJDfkPD/y4mInFO7owemKgaw1qKoMZ+6L0pyWH8CvITwAPB7nK2cBPZuAlvv2rmlaSH9YKRFxvR8SUHEXa+jUk0avaK2IEqsk+OjUGyR0gDrVMMza5e26OLS0ZbU2qKIXJpyo2BNrK3bRmkJFgvrLHJDsLZuG6UlyNXDjYKHqIOWYJK2KP49VkgHaZdMRwQnwV1HgqnmwSunob5QcwE53TpWq5LpdF/VmGXkDJqOCJqOCJqOCJqOCJqOCJpO5gV/ACJOj2+gq9VDAAAAAElFTkSuQmCC');"></div></span></div><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell   " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002738" data-grid-entity-type="bug" data-grid-field="reporter" data-grid-edit="textLabel" data-grid-value="朱海华" title_username=""><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint tapd-light-table__head-cell--fixed-right tapd-light-table__head-cell--fixed-right-last" style="--height: 40px; right: 0px;"><div class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-unlink-v2 agi-button__icon agi-button__left-icon"></i></button><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-trash-v2 agi-button__icon agi-button__left-icon"></i></button></div></div></td><td></td></tr><tr idx="2" workspace_id="22012671" id="1122012671001002706" type="bug" hoveridx="2" class="row-item row-even"><td widget="menu" class="tapd-light-table__head-cell--fixed-right" style="left: 0px;"><div class="row-cell row-cell--widget height-constraint" style="--height: 40px;"><div class="table-widget__menu"><i class="tapd-icon-more-v2"></i></div></div></td><td fieldcomponent="linkDirect" class="td--linkDirect height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell j-preview-jump  " title="1122012671001002706" data-disable-tips="" data-grid-id="1122012671001002706" data-grid-entity-type="bug" data-grid-field="id" data-grid-value="1122012671001002706"> <span class="tapd-grid-edit-link id" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002706" target="_blank" title="1002706" ondragstart="return false" copy-text=""> 1002706 </span></div></div></td><td fieldcomponent="tapd-inline-title-input" class="td--tapd-inline-title-input height-constraint" style="--height: 40px;"><div data-v-0fb95900="" class="tapd-inline-title-input"><div data-v-0fb95900="" title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div data-v-0fb95900="" class="title-input__prefix"><!----><!----><span data-v-0fb95900="" class="workitem-icon" style="background-color: rgb(248, 94, 94);">BUG</span><!----></div><div data-v-0fb95900="" class="title-input__link title-input__link--status-done"><span data-v-0fb95900="" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002706" target="_blank" ondragstart="return false" copy-text="" class="tapd-grid-edit-link title-input__link--clickable"> 【WEB】【数据工作台】批量导出的病历字体不需要加粗 </span><!----></div><div data-v-0fb95900="" class="title-input__suffix"><!----><span data-v-0fb95900="" class="title-icon__item"><a data-v-0fb95900=""><!----></a></span><!----><!----><!----><i data-v-0fb95900="" slot="icon" class="title-icon__editor t2-action-button__left-icon tapd-icon-edit-v2"></i></div><!----><!----></div></div><!----><!----></div></div></td><td fieldcomponent="tapd-inline-priority-select" class="td--tapd-inline-priority-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-priority-label-v2-wrapper tapd-grid-edit-cell--editable " title="中" data-disable-tips="" data-grid-id="1122012671001002706" data-grid-entity-type="bug" data-grid-field="priority" data-grid-edit="tapd-inline-priority-select" data-grid-value="medium"><div class="tapd-priority-label-v2 colorful-labels"><span class="colorful-labels__item" style="color: rgb(255, 255, 255); background-color: rgb(40, 171, 128); border: 1px solid rgba(40, 171, 128, 0.06);"><span class="colorful-labels__item-text">中</span></span></div></div></div></td><td fieldcomponent="tapd-inline-severity-select" class="td--tapd-inline-severity-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell tapd-severity-select__label tapd-grid-edit-cell--severitySelect tapd-grid-edit-cell--editable " title="一般" data-disable-tips="" data-grid-id="1122012671001002706" data-grid-entity-type="bug" data-grid-field="severity" data-grid-edit="tapd-inline-severity-select" data-grid-value="normal"><div class="tapd-severity-select__colorable tapd-severity-select__label--normal">一般</div></div></div></td><td fieldcomponent="workflow" class="td--workflow height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell undefined tapd-grid-edit-cell--workflow tapd-grid-edit-cell--editable " title="已关闭" data-disable-tips="" data-grid-id="1122012671001002706" data-grid-entity-type="bug" data-grid-field="status" data-grid-edit="workflow" data-grid-value="closed"><div type="button" class="el-button el-button--default capsule capsule--end  " value="closed" field="status" title="已关闭" data-disable-tips="" style="null">
                  <span>已关闭</span>
                  </div></div></div></td><td fieldcomponent="tapd-inline-tree-select" class="td--tapd-inline-tree-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="web-2023.7" data-disable-tips="" data-grid-id="1122012671001002706" data-grid-entity-type="bug" data-grid-field="iteration_id" data-grid-html="" data-grid-edit="tapd-inline-tree-select" data-grid-value="web-2023.7"><span class="tapd-grid-edit-link" href="https://www.tapd.cn/22012671/prong/iterations/card_view/1122012671001000196" target="_blank" ondragstart="return false">web-2023.7</span> <i style="display: " class="tapd-icon-arrow-down-v2"></i></div></div></td><td fieldcomponent="tapd-inline-user-select" class="td--tapd-inline-user-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002706" data-grid-entity-type="bug" data-grid-field="current_owner" data-grid-edit="tapd-inline-user-select" data-grid-value="朱海华" title_username=""><div class="avatar-wrapper"><span class="agi-avatar agi-avatar--type-image agi-avatar--size-minimum"><div class="agi-avator-inner" style="background-image:url('https://www.tapd.cn/22012671/users/avatar/朱海华'), url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHSSURBVHgB7ZhNS8NAEIZHDdr6AYq1lii1VEGPHvz1XryLBw8KEdpaao3RVhRabZSI5l0sVKglyWZoN8wDe0mXTZ/MbmYmc6dn59+UYeYp44ig6Yig6Yig6Yig6Yig6Yig6WRe0CJmLMuivd1tskublMstqWu9/rsajaZLA/+DOGEVtEsFOjwoh5ILf66vrS6rUSxsUL15T632I3HBJlgOowa5iTcPxTEnCL7I9brEAcsZzIdbcb+yE3n+uCinBYtgtWLH+sOYi4hzwCKI8xUXnFcOZkYw//uGTRtJ9ElIktt6/TfigEXQ9Z4pLr3+gDhgEWy1PZXbohIEgUr4HLAIQu6m1oo8vx6WbL7/SRywVTKoTJDfkPD/y4mInFO7owemKgaw1qKoMZ+6L0pyWH8CvITwAPB7nK2cBPZuAlvv2rmlaSH9YKRFxvR8SUHEXa+jUk0avaK2IEqsk+OjUGyR0gDrVMMza5e26OLS0ZbU2qKIXJpyo2BNrK3bRmkJFgvrLHJDsLZuG6UlyNXDjYKHqIOWYJK2KP49VkgHaZdMRwQnwV1HgqnmwSunob5QcwE53TpWq5LpdF/VmGXkDJqOCJqOCJqOCJqOCJqOCJpO5gV/ACJOj2+gq9VDAAAAAElFTkSuQmCC');"></div></span></div><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell   " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002706" data-grid-entity-type="bug" data-grid-field="reporter" data-grid-edit="textLabel" data-grid-value="朱海华" title_username=""><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint tapd-light-table__head-cell--fixed-right tapd-light-table__head-cell--fixed-right-last" style="--height: 40px; right: 0px;"><div class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-unlink-v2 agi-button__icon agi-button__left-icon"></i></button><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-trash-v2 agi-button__icon agi-button__left-icon"></i></button></div></div></td><td></td></tr><tr idx="3" workspace_id="22012671" id="1122012671001002703" type="bug" hoveridx="3" class="row-item row-odd"><td widget="menu" class="tapd-light-table__head-cell--fixed-right" style="left: 0px;"><div class="row-cell row-cell--widget height-constraint" style="--height: 40px;"><div class="table-widget__menu"><i class="tapd-icon-more-v2"></i></div></div></td><td fieldcomponent="linkDirect" class="td--linkDirect height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell j-preview-jump  " title="1122012671001002703" data-disable-tips="" data-grid-id="1122012671001002703" data-grid-entity-type="bug" data-grid-field="id" data-grid-value="1122012671001002703"> <span class="tapd-grid-edit-link id" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002703" target="_blank" title="1002703" ondragstart="return false" copy-text=""> 1002703 </span></div></div></td><td fieldcomponent="tapd-inline-title-input" class="td--tapd-inline-title-input height-constraint" style="--height: 40px;"><div data-v-0fb95900="" class="tapd-inline-title-input"><div data-v-0fb95900="" title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div data-v-0fb95900="" class="title-input__prefix"><!----><!----><span data-v-0fb95900="" class="workitem-icon" style="background-color: rgb(248, 94, 94);">BUG</span><!----></div><div data-v-0fb95900="" class="title-input__link title-input__link--status-done"><span data-v-0fb95900="" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002703" target="_blank" ondragstart="return false" copy-text="" class="tapd-grid-edit-link title-input__link--clickable"> 【WEB】【数据工作台】批量导出的病历查体中缺少血压 </span><!----></div><div data-v-0fb95900="" class="title-input__suffix"><!----><span data-v-0fb95900="" class="title-icon__item"><a data-v-0fb95900=""><!----></a></span><!----><!----><!----><i data-v-0fb95900="" slot="icon" class="title-icon__editor t2-action-button__left-icon tapd-icon-edit-v2"></i></div><!----><!----></div></div><!----><!----></div></div></td><td fieldcomponent="tapd-inline-priority-select" class="td--tapd-inline-priority-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-priority-label-v2-wrapper tapd-grid-edit-cell--editable " title="-空-" data-disable-tips="" data-grid-id="1122012671001002703" data-grid-entity-type="bug" data-grid-field="priority" data-grid-edit="tapd-inline-priority-select" data-grid-value="-"><div class="tapd-priority-label-v2 colorful-labels"><span class="colorful-labels__item"><span class="colorful-labels__item-text colorful-labels__item-text--empty">-</span></span></div></div></div></td><td fieldcomponent="tapd-inline-severity-select" class="td--tapd-inline-severity-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell tapd-severity-select__label tapd-grid-edit-cell--severitySelect tapd-grid-edit-cell--editable " title="一般" data-disable-tips="" data-grid-id="1122012671001002703" data-grid-entity-type="bug" data-grid-field="severity" data-grid-edit="tapd-inline-severity-select" data-grid-value="normal"><div class="tapd-severity-select__colorable tapd-severity-select__label--normal">一般</div></div></div></td><td fieldcomponent="workflow" class="td--workflow height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell undefined tapd-grid-edit-cell--workflow tapd-grid-edit-cell--editable " title="已关闭" data-disable-tips="" data-grid-id="1122012671001002703" data-grid-entity-type="bug" data-grid-field="status" data-grid-edit="workflow" data-grid-value="closed"><div type="button" class="el-button el-button--default capsule capsule--end  " value="closed" field="status" title="已关闭" data-disable-tips="" style="null">
                  <span>已关闭</span>
                  </div></div></div></td><td fieldcomponent="tapd-inline-tree-select" class="td--tapd-inline-tree-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="web-2023.7" data-disable-tips="" data-grid-id="1122012671001002703" data-grid-entity-type="bug" data-grid-field="iteration_id" data-grid-html="" data-grid-edit="tapd-inline-tree-select" data-grid-value="web-2023.7"><span class="tapd-grid-edit-link" href="https://www.tapd.cn/22012671/prong/iterations/card_view/1122012671001000196" target="_blank" ondragstart="return false">web-2023.7</span> <i style="display: " class="tapd-icon-arrow-down-v2"></i></div></div></td><td fieldcomponent="tapd-inline-user-select" class="td--tapd-inline-user-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002703" data-grid-entity-type="bug" data-grid-field="current_owner" data-grid-edit="tapd-inline-user-select" data-grid-value="朱海华" title_username=""><div class="avatar-wrapper"><span class="agi-avatar agi-avatar--type-image agi-avatar--size-minimum"><div class="agi-avator-inner" style="background-image:url('https://www.tapd.cn/22012671/users/avatar/朱海华'), url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHSSURBVHgB7ZhNS8NAEIZHDdr6AYq1lii1VEGPHvz1XryLBw8KEdpaao3RVhRabZSI5l0sVKglyWZoN8wDe0mXTZ/MbmYmc6dn59+UYeYp44ig6Yig6Yig6Yig6Yig6Yig6WRe0CJmLMuivd1tskublMstqWu9/rsajaZLA/+DOGEVtEsFOjwoh5ILf66vrS6rUSxsUL15T632I3HBJlgOowa5iTcPxTEnCL7I9brEAcsZzIdbcb+yE3n+uCinBYtgtWLH+sOYi4hzwCKI8xUXnFcOZkYw//uGTRtJ9ElIktt6/TfigEXQ9Z4pLr3+gDhgEWy1PZXbohIEgUr4HLAIQu6m1oo8vx6WbL7/SRywVTKoTJDfkPD/y4mInFO7owemKgaw1qKoMZ+6L0pyWH8CvITwAPB7nK2cBPZuAlvv2rmlaSH9YKRFxvR8SUHEXa+jUk0avaK2IEqsk+OjUGyR0gDrVMMza5e26OLS0ZbU2qKIXJpyo2BNrK3bRmkJFgvrLHJDsLZuG6UlyNXDjYKHqIOWYJK2KP49VkgHaZdMRwQnwV1HgqnmwSunob5QcwE53TpWq5LpdF/VmGXkDJqOCJqOCJqOCJqOCJqOCJpO5gV/ACJOj2+gq9VDAAAAAElFTkSuQmCC');"></div></span></div><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell   " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002703" data-grid-entity-type="bug" data-grid-field="reporter" data-grid-edit="textLabel" data-grid-value="朱海华" title_username=""><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint tapd-light-table__head-cell--fixed-right tapd-light-table__head-cell--fixed-right-last" style="--height: 40px; right: 0px;"><div class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-unlink-v2 agi-button__icon agi-button__left-icon"></i></button><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-trash-v2 agi-button__icon agi-button__left-icon"></i></button></div></div></td><td></td></tr><tr idx="4" workspace_id="22012671" id="1122012671001002700" type="bug" hoveridx="4" class="row-item row-even"><td widget="menu" class="tapd-light-table__head-cell--fixed-right" style="left: 0px;"><div class="row-cell row-cell--widget height-constraint" style="--height: 40px;"><div class="table-widget__menu"><i class="tapd-icon-more-v2"></i></div></div></td><td fieldcomponent="linkDirect" class="td--linkDirect height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell j-preview-jump  " title="1122012671001002700" data-disable-tips="" data-grid-id="1122012671001002700" data-grid-entity-type="bug" data-grid-field="id" data-grid-value="1122012671001002700"> <span class="tapd-grid-edit-link id" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002700" target="_blank" title="1002700" ondragstart="return false" copy-text=""> 1002700 </span></div></div></td><td fieldcomponent="tapd-inline-title-input" class="td--tapd-inline-title-input height-constraint" style="--height: 40px;"><div data-v-0fb95900="" class="tapd-inline-title-input"><div data-v-0fb95900="" title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div data-v-0fb95900="" class="title-input__prefix"><!----><!----><span data-v-0fb95900="" class="workitem-icon" style="background-color: rgb(248, 94, 94);">BUG</span><!----></div><div data-v-0fb95900="" class="title-input__link title-input__link--status-done"><span data-v-0fb95900="" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002700" target="_blank" ondragstart="return false" copy-text="" class="tapd-grid-edit-link title-input__link--clickable"> 【WEB】【数据工作台】批量导出病历，将患者简称中的“X”改成“某”，如 李某 </span><!----></div><div data-v-0fb95900="" class="title-input__suffix"><!----><span data-v-0fb95900="" class="title-icon__item"><a data-v-0fb95900=""><!----></a></span><!----><!----><!----><i data-v-0fb95900="" slot="icon" class="title-icon__editor t2-action-button__left-icon tapd-icon-edit-v2"></i></div><!----><!----></div></div><!----><!----></div></div></td><td fieldcomponent="tapd-inline-priority-select" class="td--tapd-inline-priority-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-priority-label-v2-wrapper tapd-grid-edit-cell--editable " title="中" data-disable-tips="" data-grid-id="1122012671001002700" data-grid-entity-type="bug" data-grid-field="priority" data-grid-edit="tapd-inline-priority-select" data-grid-value="medium"><div class="tapd-priority-label-v2 colorful-labels"><span class="colorful-labels__item" style="color: rgb(255, 255, 255); background-color: rgb(40, 171, 128); border: 1px solid rgba(40, 171, 128, 0.06);"><span class="colorful-labels__item-text">中</span></span></div></div></div></td><td fieldcomponent="tapd-inline-severity-select" class="td--tapd-inline-severity-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell tapd-severity-select__label tapd-grid-edit-cell--severitySelect tapd-grid-edit-cell--editable " title="一般" data-disable-tips="" data-grid-id="1122012671001002700" data-grid-entity-type="bug" data-grid-field="severity" data-grid-edit="tapd-inline-severity-select" data-grid-value="normal"><div class="tapd-severity-select__colorable tapd-severity-select__label--normal">一般</div></div></div></td><td fieldcomponent="workflow" class="td--workflow height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell undefined tapd-grid-edit-cell--workflow tapd-grid-edit-cell--editable " title="已关闭" data-disable-tips="" data-grid-id="1122012671001002700" data-grid-entity-type="bug" data-grid-field="status" data-grid-edit="workflow" data-grid-value="closed"><div type="button" class="el-button el-button--default capsule capsule--end  " value="closed" field="status" title="已关闭" data-disable-tips="" style="null">
                  <span>已关闭</span>
                  </div></div></div></td><td fieldcomponent="tapd-inline-tree-select" class="td--tapd-inline-tree-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="web-2023.7" data-disable-tips="" data-grid-id="1122012671001002700" data-grid-entity-type="bug" data-grid-field="iteration_id" data-grid-html="" data-grid-edit="tapd-inline-tree-select" data-grid-value="web-2023.7"><span class="tapd-grid-edit-link" href="https://www.tapd.cn/22012671/prong/iterations/card_view/1122012671001000196" target="_blank" ondragstart="return false">web-2023.7</span> <i style="display: " class="tapd-icon-arrow-down-v2"></i></div></div></td><td fieldcomponent="tapd-inline-user-select" class="td--tapd-inline-user-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002700" data-grid-entity-type="bug" data-grid-field="current_owner" data-grid-edit="tapd-inline-user-select" data-grid-value="朱海华" title_username=""><div class="avatar-wrapper"><span class="agi-avatar agi-avatar--type-image agi-avatar--size-minimum"><div class="agi-avator-inner" style="background-image:url('https://www.tapd.cn/22012671/users/avatar/朱海华'), url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHSSURBVHgB7ZhNS8NAEIZHDdr6AYq1lii1VEGPHvz1XryLBw8KEdpaao3RVhRabZSI5l0sVKglyWZoN8wDe0mXTZ/MbmYmc6dn59+UYeYp44ig6Yig6Yig6Yig6Yig6Yig6WRe0CJmLMuivd1tskublMstqWu9/rsajaZLA/+DOGEVtEsFOjwoh5ILf66vrS6rUSxsUL15T632I3HBJlgOowa5iTcPxTEnCL7I9brEAcsZzIdbcb+yE3n+uCinBYtgtWLH+sOYi4hzwCKI8xUXnFcOZkYw//uGTRtJ9ElIktt6/TfigEXQ9Z4pLr3+gDhgEWy1PZXbohIEgUr4HLAIQu6m1oo8vx6WbL7/SRywVTKoTJDfkPD/y4mInFO7owemKgaw1qKoMZ+6L0pyWH8CvITwAPB7nK2cBPZuAlvv2rmlaSH9YKRFxvR8SUHEXa+jUk0avaK2IEqsk+OjUGyR0gDrVMMza5e26OLS0ZbU2qKIXJpyo2BNrK3bRmkJFgvrLHJDsLZuG6UlyNXDjYKHqIOWYJK2KP49VkgHaZdMRwQnwV1HgqnmwSunob5QcwE53TpWq5LpdF/VmGXkDJqOCJqOCJqOCJqOCJqOCJpO5gV/ACJOj2+gq9VDAAAAAElFTkSuQmCC');"></div></span></div><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell   " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002700" data-grid-entity-type="bug" data-grid-field="reporter" data-grid-edit="textLabel" data-grid-value="朱海华" title_username=""><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint tapd-light-table__head-cell--fixed-right tapd-light-table__head-cell--fixed-right-last" style="--height: 40px; right: 0px;"><div class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-unlink-v2 agi-button__icon agi-button__left-icon"></i></button><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-trash-v2 agi-button__icon agi-button__left-icon"></i></button></div></div></td><td></td></tr><tr idx="5" workspace_id="22012671" id="1122012671001002699" type="bug" hoveridx="5" class="row-item row-odd"><td widget="menu" class="tapd-light-table__head-cell--fixed-right" style="left: 0px;"><div class="row-cell row-cell--widget height-constraint" style="--height: 40px;"><div class="table-widget__menu"><i class="tapd-icon-more-v2"></i></div></div></td><td fieldcomponent="linkDirect" class="td--linkDirect height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell j-preview-jump  " title="1122012671001002699" data-disable-tips="" data-grid-id="1122012671001002699" data-grid-entity-type="bug" data-grid-field="id" data-grid-value="1122012671001002699"> <span class="tapd-grid-edit-link id" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002699" target="_blank" title="1002699" ondragstart="return false" copy-text=""> 1002699 </span></div></div></td><td fieldcomponent="tapd-inline-title-input" class="td--tapd-inline-title-input height-constraint" style="--height: 40px;"><div data-v-0fb95900="" class="tapd-inline-title-input"><div data-v-0fb95900="" title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div data-v-0fb95900="" class="title-input__prefix"><!----><!----><span data-v-0fb95900="" class="workitem-icon" style="background-color: rgb(248, 94, 94);">BUG</span><!----></div><div data-v-0fb95900="" class="title-input__link title-input__link--status-done"><span data-v-0fb95900="" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002699" target="_blank" ondragstart="return false" copy-text="" class="tapd-grid-edit-link title-input__link--clickable"> 【WEB】【全科室工作台】给患者新建病历，可将页面滚动出多余区域 </span><!----></div><div data-v-0fb95900="" class="title-input__suffix"><!----><span data-v-0fb95900="" class="title-icon__item"><a data-v-0fb95900=""><!----></a></span><!----><!----><!----><i data-v-0fb95900="" slot="icon" class="title-icon__editor t2-action-button__left-icon tapd-icon-edit-v2"></i></div><!----><!----></div></div><!----><!----></div></div></td><td fieldcomponent="tapd-inline-priority-select" class="td--tapd-inline-priority-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-priority-label-v2-wrapper tapd-grid-edit-cell--editable " title="-空-" data-disable-tips="" data-grid-id="1122012671001002699" data-grid-entity-type="bug" data-grid-field="priority" data-grid-edit="tapd-inline-priority-select" data-grid-value="-"><div class="tapd-priority-label-v2 colorful-labels"><span class="colorful-labels__item"><span class="colorful-labels__item-text colorful-labels__item-text--empty">-</span></span></div></div></div></td><td fieldcomponent="tapd-inline-severity-select" class="td--tapd-inline-severity-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell tapd-severity-select__label tapd-grid-edit-cell--severitySelect tapd-grid-edit-cell--editable " title="一般" data-disable-tips="" data-grid-id="1122012671001002699" data-grid-entity-type="bug" data-grid-field="severity" data-grid-edit="tapd-inline-severity-select" data-grid-value="normal"><div class="tapd-severity-select__colorable tapd-severity-select__label--normal">一般</div></div></div></td><td fieldcomponent="workflow" class="td--workflow height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell undefined tapd-grid-edit-cell--workflow tapd-grid-edit-cell--editable " title="已关闭" data-disable-tips="" data-grid-id="1122012671001002699" data-grid-entity-type="bug" data-grid-field="status" data-grid-edit="workflow" data-grid-value="closed"><div type="button" class="el-button el-button--default capsule capsule--end  " value="closed" field="status" title="已关闭" data-disable-tips="" style="null">
                  <span>已关闭</span>
                  </div></div></div></td><td fieldcomponent="tapd-inline-tree-select" class="td--tapd-inline-tree-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="web-2023.7" data-disable-tips="" data-grid-id="1122012671001002699" data-grid-entity-type="bug" data-grid-field="iteration_id" data-grid-html="" data-grid-edit="tapd-inline-tree-select" data-grid-value="web-2023.7"><span class="tapd-grid-edit-link" href="https://www.tapd.cn/22012671/prong/iterations/card_view/1122012671001000196" target="_blank" ondragstart="return false">web-2023.7</span> <i style="display: " class="tapd-icon-arrow-down-v2"></i></div></div></td><td fieldcomponent="tapd-inline-user-select" class="td--tapd-inline-user-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="-" data-disable-tips="" data-grid-id="1122012671001002699" data-grid-entity-type="bug" data-grid-field="current_owner" data-grid-edit="tapd-inline-user-select" data-grid-value="-" title_username=""><span>-</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell   " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002699" data-grid-entity-type="bug" data-grid-field="reporter" data-grid-edit="textLabel" data-grid-value="朱海华" title_username=""><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint tapd-light-table__head-cell--fixed-right tapd-light-table__head-cell--fixed-right-last" style="--height: 40px; right: 0px;"><div class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-unlink-v2 agi-button__icon agi-button__left-icon"></i></button><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-trash-v2 agi-button__icon agi-button__left-icon"></i></button></div></div></td><td></td></tr><tr idx="6" workspace_id="22012671" id="1122012671001002695" type="bug" hoveridx="6" class="row-item row-even"><td widget="menu" class="tapd-light-table__head-cell--fixed-right" style="left: 0px;"><div class="row-cell row-cell--widget height-constraint" style="--height: 40px;"><div class="table-widget__menu"><i class="tapd-icon-more-v2"></i></div></div></td><td fieldcomponent="linkDirect" class="td--linkDirect height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell j-preview-jump  " title="1122012671001002695" data-disable-tips="" data-grid-id="1122012671001002695" data-grid-entity-type="bug" data-grid-field="id" data-grid-value="1122012671001002695"> <span class="tapd-grid-edit-link id" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002695" target="_blank" title="1002695" ondragstart="return false" copy-text=""> 1002695 </span></div></div></td><td fieldcomponent="tapd-inline-title-input" class="td--tapd-inline-title-input height-constraint" style="--height: 40px;"><div data-v-0fb95900="" class="tapd-inline-title-input"><div data-v-0fb95900="" title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div data-v-0fb95900="" class="title-input__prefix"><!----><!----><span data-v-0fb95900="" class="workitem-icon" style="background-color: rgb(248, 94, 94);">BUG</span><!----></div><div data-v-0fb95900="" class="title-input__link title-input__link--status-done"><span data-v-0fb95900="" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002695" target="_blank" ondragstart="return false" copy-text="" class="tapd-grid-edit-link title-input__link--clickable"> 【WEB】【数据工作台】批量导出病历-压缩包命名应为“yyyy-mm-dd 至 yyyy-mm-dd 服务包患者病例” </span><!----></div><div data-v-0fb95900="" class="title-input__suffix"><!----><span data-v-0fb95900="" class="title-icon__item"><a data-v-0fb95900=""><!----></a></span><!----><!----><!----><i data-v-0fb95900="" slot="icon" class="title-icon__editor t2-action-button__left-icon tapd-icon-edit-v2"></i></div><!----><!----></div></div><!----><!----></div></div></td><td fieldcomponent="tapd-inline-priority-select" class="td--tapd-inline-priority-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-priority-label-v2-wrapper tapd-grid-edit-cell--editable " title="-空-" data-disable-tips="" data-grid-id="1122012671001002695" data-grid-entity-type="bug" data-grid-field="priority" data-grid-edit="tapd-inline-priority-select" data-grid-value="-"><div class="tapd-priority-label-v2 colorful-labels"><span class="colorful-labels__item"><span class="colorful-labels__item-text colorful-labels__item-text--empty">-</span></span></div></div></div></td><td fieldcomponent="tapd-inline-severity-select" class="td--tapd-inline-severity-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell tapd-severity-select__label tapd-grid-edit-cell--severitySelect tapd-grid-edit-cell--editable " title="一般" data-disable-tips="" data-grid-id="1122012671001002695" data-grid-entity-type="bug" data-grid-field="severity" data-grid-edit="tapd-inline-severity-select" data-grid-value="normal"><div class="tapd-severity-select__colorable tapd-severity-select__label--normal">一般</div></div></div></td><td fieldcomponent="workflow" class="td--workflow height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell undefined tapd-grid-edit-cell--workflow tapd-grid-edit-cell--editable " title="已关闭" data-disable-tips="" data-grid-id="1122012671001002695" data-grid-entity-type="bug" data-grid-field="status" data-grid-edit="workflow" data-grid-value="closed"><div type="button" class="el-button el-button--default capsule capsule--end  " value="closed" field="status" title="已关闭" data-disable-tips="" style="null">
                  <span>已关闭</span>
                  </div></div></div></td><td fieldcomponent="tapd-inline-tree-select" class="td--tapd-inline-tree-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="web-2023.7" data-disable-tips="" data-grid-id="1122012671001002695" data-grid-entity-type="bug" data-grid-field="iteration_id" data-grid-html="" data-grid-edit="tapd-inline-tree-select" data-grid-value="web-2023.7"><span class="tapd-grid-edit-link" href="https://www.tapd.cn/22012671/prong/iterations/card_view/1122012671001000196" target="_blank" ondragstart="return false">web-2023.7</span> <i style="display: " class="tapd-icon-arrow-down-v2"></i></div></div></td><td fieldcomponent="tapd-inline-user-select" class="td--tapd-inline-user-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002695" data-grid-entity-type="bug" data-grid-field="current_owner" data-grid-edit="tapd-inline-user-select" data-grid-value="朱海华" title_username=""><div class="avatar-wrapper"><span class="agi-avatar agi-avatar--type-image agi-avatar--size-minimum"><div class="agi-avator-inner" style="background-image:url('https://www.tapd.cn/22012671/users/avatar/朱海华'), url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHSSURBVHgB7ZhNS8NAEIZHDdr6AYq1lii1VEGPHvz1XryLBw8KEdpaao3RVhRabZSI5l0sVKglyWZoN8wDe0mXTZ/MbmYmc6dn59+UYeYp44ig6Yig6Yig6Yig6Yig6Yig6WRe0CJmLMuivd1tskublMstqWu9/rsajaZLA/+DOGEVtEsFOjwoh5ILf66vrS6rUSxsUL15T632I3HBJlgOowa5iTcPxTEnCL7I9brEAcsZzIdbcb+yE3n+uCinBYtgtWLH+sOYi4hzwCKI8xUXnFcOZkYw//uGTRtJ9ElIktt6/TfigEXQ9Z4pLr3+gDhgEWy1PZXbohIEgUr4HLAIQu6m1oo8vx6WbL7/SRywVTKoTJDfkPD/y4mInFO7owemKgaw1qKoMZ+6L0pyWH8CvITwAPB7nK2cBPZuAlvv2rmlaSH9YKRFxvR8SUHEXa+jUk0avaK2IEqsk+OjUGyR0gDrVMMza5e26OLS0ZbU2qKIXJpyo2BNrK3bRmkJFgvrLHJDsLZuG6UlyNXDjYKHqIOWYJK2KP49VkgHaZdMRwQnwV1HgqnmwSunob5QcwE53TpWq5LpdF/VmGXkDJqOCJqOCJqOCJqOCJqOCJpO5gV/ACJOj2+gq9VDAAAAAElFTkSuQmCC');"></div></span></div><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell   " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002695" data-grid-entity-type="bug" data-grid-field="reporter" data-grid-edit="textLabel" data-grid-value="朱海华" title_username=""><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint tapd-light-table__head-cell--fixed-right tapd-light-table__head-cell--fixed-right-last" style="--height: 40px; right: 0px;"><div class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-unlink-v2 agi-button__icon agi-button__left-icon"></i></button><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-trash-v2 agi-button__icon agi-button__left-icon"></i></button></div></div></td><td></td></tr><tr idx="7" workspace_id="22012671" id="1122012671001002686" type="bug" hoveridx="7" class="row-item row-odd"><td widget="menu" class="tapd-light-table__head-cell--fixed-right" style="left: 0px;"><div class="row-cell row-cell--widget height-constraint" style="--height: 40px;"><div class="table-widget__menu"><i class="tapd-icon-more-v2"></i></div></div></td><td fieldcomponent="linkDirect" class="td--linkDirect height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell j-preview-jump  " title="1122012671001002686" data-disable-tips="" data-grid-id="1122012671001002686" data-grid-entity-type="bug" data-grid-field="id" data-grid-value="1122012671001002686"> <span class="tapd-grid-edit-link id" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002686" target="_blank" title="1002686" ondragstart="return false" copy-text=""> 1002686 </span></div></div></td><td fieldcomponent="tapd-inline-title-input" class="td--tapd-inline-title-input height-constraint" style="--height: 40px;"><div data-v-0fb95900="" class="tapd-inline-title-input"><div data-v-0fb95900="" title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div data-v-0fb95900="" class="title-input__prefix"><!----><!----><span data-v-0fb95900="" class="workitem-icon" style="background-color: rgb(248, 94, 94);">BUG</span><!----></div><div data-v-0fb95900="" class="title-input__link title-input__link--status-done"><span data-v-0fb95900="" href="https://www.tapd.cn/22012671/bugtrace/bugs/view/1122012671001002686" target="_blank" ondragstart="return false" copy-text="" class="tapd-grid-edit-link title-input__link--clickable"> 【WEB】【管理员工作台】【功能集管理】导出病历功能集未添加 </span><!----></div><div data-v-0fb95900="" class="title-input__suffix"><!----><span data-v-0fb95900="" class="title-icon__item"><a data-v-0fb95900=""><!----></a></span><!----><!----><!----><i data-v-0fb95900="" slot="icon" class="title-icon__editor t2-action-button__left-icon tapd-icon-edit-v2"></i></div><!----><!----></div></div><!----><!----></div></div></td><td fieldcomponent="tapd-inline-priority-select" class="td--tapd-inline-priority-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-priority-label-v2-wrapper tapd-grid-edit-cell--editable " title="-空-" data-disable-tips="" data-grid-id="1122012671001002686" data-grid-entity-type="bug" data-grid-field="priority" data-grid-edit="tapd-inline-priority-select" data-grid-value="-"><div class="tapd-priority-label-v2 colorful-labels"><span class="colorful-labels__item"><span class="colorful-labels__item-text colorful-labels__item-text--empty">-</span></span></div></div></div></td><td fieldcomponent="tapd-inline-severity-select" class="td--tapd-inline-severity-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell tapd-severity-select__label tapd-grid-edit-cell--severitySelect tapd-grid-edit-cell--editable " title="一般" data-disable-tips="" data-grid-id="1122012671001002686" data-grid-entity-type="bug" data-grid-field="severity" data-grid-edit="tapd-inline-severity-select" data-grid-value="normal"><div class="tapd-severity-select__colorable tapd-severity-select__label--normal">一般</div></div></div></td><td fieldcomponent="workflow" class="td--workflow height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell undefined tapd-grid-edit-cell--workflow tapd-grid-edit-cell--editable " title="已关闭" data-disable-tips="" data-grid-id="1122012671001002686" data-grid-entity-type="bug" data-grid-field="status" data-grid-edit="workflow" data-grid-value="closed"><div type="button" class="el-button el-button--default capsule capsule--end  " value="closed" field="status" title="已关闭" data-disable-tips="" style="null">
                  <span>已关闭</span>
                  </div></div></div></td><td fieldcomponent="tapd-inline-tree-select" class="td--tapd-inline-tree-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="web-2023.7" data-disable-tips="" data-grid-id="1122012671001002686" data-grid-entity-type="bug" data-grid-field="iteration_id" data-grid-html="" data-grid-edit="tapd-inline-tree-select" data-grid-value="web-2023.7"><span class="tapd-grid-edit-link" href="https://www.tapd.cn/22012671/prong/iterations/card_view/1122012671001000196" target="_blank" ondragstart="return false">web-2023.7</span> <i style="display: " class="tapd-icon-arrow-down-v2"></i></div></div></td><td fieldcomponent="tapd-inline-user-select" class="td--tapd-inline-user-select height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell  tapd-grid-edit-cell--editable " title="-" data-disable-tips="" data-grid-id="1122012671001002686" data-grid-entity-type="bug" data-grid-field="current_owner" data-grid-edit="tapd-inline-user-select" data-grid-value="-" title_username=""><span>-</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint" style="--height: 40px;"><div opendelay="true" class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell   " title="朱海华" data-disable-tips="" data-grid-id="1122012671001002686" data-grid-entity-type="bug" data-grid-field="reporter" data-grid-edit="textLabel" data-grid-value="朱海华" title_username=""><span>朱海华</span></div></div></td><td fieldcomponent="textLabel" class="td--textLabel height-constraint tapd-light-table__head-cell--fixed-right tapd-light-table__head-cell--fixed-right-last" style="--height: 40px; right: 0px;"><div class="row-cell" style="height: 40px; line-height: 40px;"><div class="tapd-grid-edit-cell"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-unlink-v2 agi-button__icon agi-button__left-icon"></i></button><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-trash-v2 agi-button__icon agi-button__left-icon"></i></button></div></div></td><td></td></tr></tbody></table></div></div></div><span><div class="tapd-table__foot-wrap"><div class="tapd-table__foot" style="line-height: 40px;"><div class="tapd-table-pagination"><!----></div></div></div></span></div><!----></div><div class="table-widget__menu-popper" style="display: none;"><div class="menu-head" style="display: none;"> 批量处理() </div><div class="menu-body"><ul><div class="agi-dropdown" style="width: 100%;"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-4246" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover " tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><li class="menu-item-with-sub-menu agi-dropdown-menu-custom-item el-popover__reference" aria-describedby="el-popover-4246" tabindex="0"><i class="menu-item-with-sub-menu__left-icon agi-icon tapd-icon-edit-v2"></i>批量编辑<span class="menu-item-submenu-indicator"><i class="menu-item-submenu-indicator__icon agi-icon tapd-icon-arrow-down-v2"></i></span></li></span></span></div><li class="agi-dropdown-menu-item" name="batchCopyTitleAndLinkMenuItem" title="批量复制标题&amp;链接"><i class="agi-icon tapd-icon-link-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text">批量复制标题&amp;链接</span></li><li class="agi-dropdown-menu-item" name="batchCopyLinkMenuItem" title="批量复制链接"><i class="agi-icon tapd-icon-link-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text">批量复制链接</span></li><div class="agi-dropdown-menu-divider"></div><li class="agi-dropdown-menu-item" name="batchArchiveMenuItem"><i class="agi-icon tapd-icon-archive-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text">批量归档</span></li><li class="agi-dropdown-menu-item agi-dropdown-menu-item--dangerous" name="batchDeleteMenuItem"><i class="agi-icon tapd-icon-trash-v2 agi-dropdown-menu-item__icon"></i><span class="agi-dropdown-menu-item__text">批量删除</span></li></ul></div></div><div class="el-loading-mask" style="display: none;"><div class="el-loading-spinner spinner--tapd-loading"><i class="tapd-loading"></i><!----></div></div></div></div><!----><!----><!----></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><div data-v-82484c12="" class="tab-detail-item detail-item__content" detaildata="[object Object]" mainlocation="/prong/stories/stories_list" fromiterationid="" style=""><div class="detail-item__content-wrapper"><div class="detail-item__parent"><div><div class="detail-item__parent--header"><div class="parent-story-selector"><span>上级需求: </span><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001003539" target="_blank">【web端(新增)】单品销售数据 </a></div></div><div class="detail-item__parent--expand" parent-id="1122012671001003539"> 展开详情 <i class="tapd-icon-arrow-down-v2"></i></div></div><!----><!----></div><div class="content-wrap"><div tabindex="-1" class="cherry-editor-content tex2jax_ignore"><h3 style="word-break: break-word; margin: 10px 0px 1em; padding: 0px; font: bold 18px / 1.5 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; color: #34383e; height: auto; cursor: text; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;">一、背景</h3><p>系统不支持批量导出病例，照护师需通过研发同学，才能达成批量导出病例的目的。</p><p><br></p><h3 style="word-break: break-word; margin: 10px 0px 1em; padding: 0px; font: bold 18px / 1.5 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; color: #34383e; height: auto; cursor: text; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;">二、要解决的问题</h3><p>通过新增“批量导出病例”的功能，提高照护师的操作效率。</p><p><br></p><h3 style="word-break: break-word; margin: 10px 0px 1em; padding: 0px; font: bold 18px / 1.5 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; color: #34383e; height: auto; cursor: text; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">三、功能说明</span></h3><h4><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">（一）哪些人有批量导出的权限？</span></h4><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">在【“管理员工作台”——“权限配置”】模块中增加对【“数据工作台”——“销售数据”——“单体商品数据”——“导出功能”】权限的配置。</span></p><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">1.功能入口</span></p><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">共同照护web端 - 管理员工作台 - 权限配置</span></p><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;"><br></span></p><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">2.功能说明</span></p><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">（1）如下图所示，在此处新增权限集配置：</span></p><div class="tox-clear-float"><img title="点击看原图" href="https://file.tapd.cn/tfl/captures/2023-07/tapd_22012671_base64_1689146909_498.png" class="compress gallery" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" original_src="https://file.tapd.cn//tfl/captures/2023-07/tapd_22012671_base64_1689146909_498.png" style="max-width:600px;height:338px;" disable-lazyload="true"></div><div class="tox-clear-float">功能名称：“导出-导出病例功能”；</div><div class="tox-clear-float">可操作：“操作”；</div><div class="tox-clear-float">（2）如下图所示，对应控制权限的功能按钮为“批量导出病例”按钮：</div><div class="tox-clear-float"><div class="tox-clear-float"><div class="tox-clear-float"><div class="tox-clear-float"><div class="tox-clear-float"><div class="tox-clear-float"><img title="点击看原图" href="https://file.tapd.cn/tfl/captures/2023-07/tapd_22012671_base64_1689157120_777.png" class="compress gallery" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" original_src="https://file.tapd.cn//tfl/captures/2023-07/tapd_22012671_base64_1689157120_777.png" style="max-width:600px;height:303px;" disable-lazyload="true"></div></div></div></div></div></div><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">如果该用户未有权限，“批量导出病例——批量导出病例”按钮不显示；</span></p><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">如果该用户有权限，“批量导出病例——批量导出病例”按钮显示，且可操作；</span></p><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;"><br></span></p><h4><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">（二）</span><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">如何批量导出病例？</span></h4><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">在【“数据工作台”——“销售数据”——“单体商品数据”——“导出功能”】的按钮下拉列表中（用户点击“下拉箭头”即出现）新增一个“批量导出病例”功能按钮，用户点击按钮，即可触发【将患者的病例打包为zip压缩包】进行下载，zip压缩包名为“yyyy-mm-dd 至 yyyy-mm-dd 服务包患者病例”；</span></p><div class="tox-clear-float"><div class="tox-clear-float"><img title="点击看原图" href="https://file.tapd.cn/tfl/captures/2023-07/tapd_22012671_base64_1689157053_454.png" class="compress gallery" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" original_src="https://file.tapd.cn//tfl/captures/2023-07/tapd_22012671_base64_1689157053_454.png" style="max-width:600px;height:303px;" disable-lazyload="true"></div></div><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;"><br></span></p><h4><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">（三）能导出哪些患者的病例？</span></h4><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">1.操作人员能够导出的为【自己管辖权限下的，已购买”服务包“商品】的患者的病例，包括：</span></p><ul><li><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">跨医生、跨医护组、跨医院的【已购买”服务包“商品】用户的病例数据；</span></li><li><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">一次或多次的【已购买”服务包“商品】用户的病例数据（例如，某患者在购买服务包后复诊了两次，则此处下载两份病例）；</span><br></li><li><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">根据用户在“时间控件”选择的【时间段范围】，展示符合【时间段范围】内的最近病例数据；</span></li><li><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">取购买服务包时间下的最近一次病历</span></li></ul><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">2.下载的【病例】数据的要求为：</span></p><ul><li><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">下载的为将所有患者的病例打包的zip压缩包，zip压缩包名为“yyyy-mm-dd 至 yyyy-mm-dd 服务包患者病例”；</span></li><li><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">患者每一份病例作为一个单独的文件放进zip压缩包；</span></li><li><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">病例文件的命名为【“患者姓名（显示患者姓氏，姓名用“某某”打码，例如侯某）” + ﹝-﹞+ “医院简称（例如，北大医院）” 】</span></li><li><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">在压缩包的文件中，同一患者的病例展示在同一处（方便照护师快速了解，同一患者复诊次数）</span></li></ul><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;"><br></span></p><h4><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">（四）导出的病例如何展现？</span></h4><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">导出的每份单独的病例数据的文件中，需包括【姓名、年龄、身高、体重、BMI、主诉、现病史、既往史（包括既往史、合并症、家族史）、查体（包括BP、BW、BH、BMI、HEENT、NECK、CHEST、HEART、ABD及EXT）、检验（包括空腹血糖、餐后2小时血糖、随机血糖、HbAlc、Cr、LDL-c、eGFR）、诊断、处方】等字段；</span></p><p><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">注：“导出功能”示范文件请见附件</span></p><div class="tox-clear-float"><div><div><div><div><div><div><div class="scroll_table"><div class="scroll_table"><table border="1" style="border-collapse: collapse; border-width: 2px; border-color: #ced4d9; width: 978px; height: 854px;"><tbody><tr style="height: 28px;"><td style="width: 83px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">一级模块</td><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">二级模块</td><td style="width: 257px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">【系统病例】编辑时</td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">下载的【病例文件】中</td></tr><tr style="height: 28px;"><td style="width: 169px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">姓名</span><br></td><td style="width: 257px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">系统默认记挂在某个患者下；</td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p>展示“姓名：”字段，数据源取自病例所属的患者姓名；</p><p>需自动打码，只保留患者姓氏，名字用“某某”替代；</p><p>格式为：【“姓氏”+“某”】或【“姓氏”+“某某”】；</p></td></tr><tr style="height: 28px;"><td style="width: 169px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">年龄</span><br></td><td style="width: 257px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">系统默认计算；</td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p>通过系统中患者资料中的“出生年”，减去“当前年”，得出的结果为该患者的年龄；</p><p>格式为：【xx（xx为正整数）】；</p></td></tr><tr style="height: 28px;"><td style="width: 169px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">身高(cm)</span></td><td style="width: 257px; border-width: 2px; padding-top: 4px; padding-right: 4px; padding-bottom: 4px; border-color: #ced4d9; height: 56px;" rowspan="2">在门诊现场时，照护师记录门诊查体结果；<br></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p>数据源取自患者当次门诊的【“门诊查体数据”中的“身高”】；</p><p>格式为：【xx（xx为正整数）】；</p></td></tr><tr style="height: 28px;"><td style="border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">体重(kg)</span><br></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p>数据源取自患者当次门诊的【“门诊查体数据”中的“体重”】；</p><p>格式为：【xx（xx为正整数）】；</p></td></tr><tr style="height: 28px;"><td style="width: 169px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">BMI</span><br></td><td style="width: 257px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p>根据门诊现场，照护师记录的门诊查体结果，并通过固定公式计算得出；</p><p>计算公式为：</p><div class="tox-clear-float"><img title="点击看原图" href="https://file.tapd.cn/tfl/captures/2023-07/tapd_22012671_base64_1689155631_195.png" class="compress gallery" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" original_src="https://file.tapd.cn//tfl/captures/2023-07/tapd_22012671_base64_1689155631_195.png" style="max-width:257px;height:26px;" disable-lazyload="true"></div></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p>数据源取自患者当次门诊的身高和体重，并根据：<img title="点击看原图" href="https://file.tapd.cn/tfl/captures/2023-07/tapd_22012671_base64_1689155657_752.png" class="compress gallery" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" original_src="https://file.tapd.cn//tfl/captures/2023-07/tapd_22012671_base64_1689155657_752.png" style="max-width:80%;" disable-lazyload="true">，计算出用户的BMI；</p><p>格式为【xx（xx为正整数）】；</p></td></tr><tr style="height: 28px;"><td style="width: 169px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">主诉</span><br></td><td style="width: 257px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" rowspan="26">由系统默认代入上次门诊的病例结果值，再由医生或照护师手动修改；<br></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p>数据源取自患者当次就诊病例中的【主诉】内容；</p><p>格式为：</p><p>【主诉</p><p>糖尿病类型：{用户糖尿病类型}；病程自{用户选择的起始年份、月份}</p><p>患者自述：{病程下方的输入框中用户输入的内容}】</p></td></tr><tr style="height: 28px;"><td style="width: 169px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">现病史</span><br></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p>数据源取自患者当次就诊病例中的【现病史】内容；</p><p>格式为：</p><p>【现病史：</p><p class="p1"><span class="s1">{疾病名称}，疾病自：</span><span class="s1">{起始年份} 年，{附注说明}</span></p><p class="p1"><span class="s1">{备注说明}】</span></p></td></tr><tr><td style="width: 83px; border-width: 2px; padding: 4px; border-color: #ced4d9;" rowspan="5"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">既往史</span><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;"><br></span></td><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9;"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">既往史</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9;"><p>数据源取自患者当次就诊病例中的【既往史-既往史】内容；</p><p>格式为：</p><p>【既往史：</p><p>（如果“否既往史”为oncheck状态，即展示为）否认既往史；</p><p>（如果“否既往史”为uncheck状态，即展示为）<span class="s1">{疾病名称}，疾病自：</span><span class="s1">{起始年份} 年，{附注说明}</span>】</p></td></tr><tr><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9;"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">合并症</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9;"><p class="p1"><span class="s1">数据源取自患者当次就诊病例中的【既往史-合并症】内容；</span></p><p class="p1"><span class="s1">格式为：</span></p><p class="p1"><span class="s1">【</span>（如果“否<span class="s1">合并症</span>”为oncheck状态，即展示为）否认<span class="s1">合并症</span>；</p><p>（如果“否<span class="s1">合并症</span>”为uncheck状态，即展示为）<span class="s1">{疾病名称}，疾病自：</span><span class="s1">{起始年份} 年，{附注说明}】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">吸烟史</td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【既往史-吸烟史】内容；</span></p><p><span class="s1">格式为：</span></p><p class="p1"><span class="s1">【</span>（如果“否<span class="s1">吸烟史</span>”为oncheck状态，即展示为）否认<span class="s1">吸烟史</span>；</p><p>（如果“否<span class="s1">吸烟史</span>”为uncheck状态，即展示为）烟龄：<span class="s1">{年数} 年；每日吸烟量：{支数} 支；</span></p><p><span class="s1">】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">饮酒史</td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【既往史-饮酒史】内容；</span></p><p><span class="s1">格式为：</span></p><p class="p1"><span class="s1">【</span>（如果“否<span class="s1">饮酒史</span>”为oncheck状态，即展示为）否认<span class="s1">饮酒史</span>；</p><p>（如果“否<span class="s1">饮酒史</span>”为uncheck状态，即展示为）酒领：<span class="s1">{年数} 年；每日饮酒量：{两数} 两（注：1两=50毫升）；</span></p><p><span class="s1">】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">家族史</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【既往史-<span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">家族</span>史】内容；</span></p><p><span class="s1">格式为：</span></p><p class="p1"><span class="s1">【</span>（如果“否<span class="s1"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">家族</span>史</span>”为oncheck状态，即展示为）否认<span class="s1"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">家族</span>史</span>；</p><p>（如果“否<span class="s1"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">家族</span>史</span>”为uncheck状态，即展示为）<span class="s1">{血缘关系（例如，母亲）} {疾病名称} 病程自{年数}年；{附注说明}</span><span class="s1">】</span></p></td></tr><tr style="height: 28px;"><td style="width: 83px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 280px;" rowspan="10"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">查体</span><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;"><br></span></td><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">BP<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">血压</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊时，门诊查体中的【诊室血压】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【BP<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">血压</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)：{诊室高压}/{诊室低压}mmHg</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">BW<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">体重</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊时，门诊查体中的【诊室体重】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【BW<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">体重</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：{诊室体重}kg</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">BH<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">身高</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊时，门诊查体中的【诊室身高】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【BH<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">身高</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：{诊室身高}cm</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">BMI</td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊时，门诊查体中的【诊室BMI】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【BMI<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：{诊室BMI}kg/m2（2为上标）</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">HEENT<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">五官</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【查体-HEENT<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">五官</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【HEENT<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">五官</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)：{病例查体结果}</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">NECK<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">颈部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【查体-NECK<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">颈部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【NECK<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">颈部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：{病例查体结果}</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">CHEST<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">胸部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【查体-CHEST<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">胸部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【CHEST<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">胸部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：{病例查体结果}</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">HEART<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">心率</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【查体-HEART<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">心率</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【HEART<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">心率</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：{病例查体结果}</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">ABD<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">腹部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【查体-ABD<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">腹部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【ABD<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">腹部</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：{病例查体结果}</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;">EXT<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">四肢</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【查体-EXT<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">四肢</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【EXT<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">(</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">四肢</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">)</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：{病例查体结果}</span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 83px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 210px;" rowspan="7"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">检验</span><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;"><br></span></td><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">空腹血糖</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【检验-<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">空腹血糖</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">空腹血糖：</span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“空腹血糖”输入框为空，则展示）未测量；</span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“空腹血糖”输入框录入具体数字，则展示）<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">{血糖数字}mmol/L；</span></span></span><span class="s1">】</span></p></td></tr><tr style="height: 42px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 42px;"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">餐后</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">2</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">小时血糖</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 42px;"><p><span class="s1">数据源取自患者当次就诊病例中的【检验-<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">餐后</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">2</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">小时血糖</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">餐后</span><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">2</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">小时血糖</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：</span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“餐后<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">2</span>小时血糖”输入框为空，则展示）未测量；</span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“餐后<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">2</span>小时血糖”输入框录入具体数字，则展示）<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">{血糖数字}mmol/L；</span></span></span><span class="s1">】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">随机血糖</span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【检验-<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">随机血糖</span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">随机血糖</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：</span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“随机血糖”输入框为空，则展示）未测量；</span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“随机血糖”输入框录入具体数字，则展示）<span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">{血糖数字}mmol/L；</span></span></span><span class="s1">】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">HbAlc</span></span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【检验-<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">HbAlc</span></span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">HbAlc：</span></span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“HbAlc”有显示默认显示的值，则展示）{数字}%；</span></span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“HbAlc”无显示默认显示的值，则展示）无前次结果；</span></span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">Cr</span></span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【检验-<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">Cr</span></span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">Cr</span></span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：</span></span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“Cr”有显示默认显示的值，则展示）{数字}<span style="color: #333333; font-family: Arial, sans-serif; font-size: 13px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: #ffffff; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;">μmol</span>；</span></span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“Cr”无显示默认显示的值，则展示）无前次结果；</span></span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">LDL-c</span></span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【检验-<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">LDL-c</span></span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">LDL-c</span></span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：</span></span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“LDL-c”有显示默认显示的值，则展示）{数字}mmol/L；</span></span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“LDL-c”无显示默认显示的值，则展示）无前次结果；</span></span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 86px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">eGFR</span></span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【检验-<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">eGFR</span></span>】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">eGFR</span></span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">：</span></span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“eGFR”有显示默认显示的值，则展示）{数字}ml/min/{数字}m2（2为上标）；</span></span></span></p><p><span class="s1"><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">（如果“eGFR”无显示默认显示的值，则展示）无前次结果；</span></span>】</span></p></td></tr><tr style="height: 28px;"><td style="width: 169px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">诊断</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><br></span></span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【诊断】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【诊断：<span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;">{输入框内容}；】</span></span></span><span class="s1"></span></p></td></tr><tr style="height: 28px;"><td style="width: 169px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;" colspan="2"><span style="word-break: break-word; line-height: inherit; background-color: #ffffff;">处方</span><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; mso-font-kerning: 0.00px; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;"><br></span></span></td><td style="width: 519px; border-width: 2px; padding: 4px; border-color: #ced4d9; height: 28px;"><p><span class="s1">数据源取自患者当次就诊病例中的【处方】内容；</span></p><p><span class="s1">格式为：</span></p><p><span class="s1">【处方：</span></p><p><span class="s1">（如果“常规药物治疗”为oncheck状态，即展示为）常规药物治疗（否则，则不展示，如果“胰岛素泵治疗”为oncheck状态，即在“常规药物治疗”后加上括号，括号内填入）（胰岛素泵治疗）；</span></p><p><span class="s1">（如果“中药治疗”为oncheck状态，即展示为）中药治疗（否则，则不展示）；</span></p><p><span class="s1">（如果“仅生活方式干预”为oncheck状态，即展示为）仅生活方式干预（否则，则不展示）；</span></p><p><span class="s1">治疗方式：</span></p><p><span class="s1">（如果“药物数据”为空时，即展示为）无数据；</span></p><p><span class="s1">（如果“药物数据”有数据时，即展示为）{药物名称} {药物剂量（例如，1片） {药物用法（例如，tid）} </span></p><p><span class="s1">（如果“药物剂量”和“药物用法”有改变时，在“药物用法”后加上括号，括号内填入“药物剂量”和“药物用法”修改对比，例如， (1g qw---&gt;1mg qw)；如果有上一个病例中存在的药物，但此次病例中不再开具，则对药物进行删除线，例如，</span><s><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; color: #9a9a9a; mso-font-kerning: 0.00px;">甘草酸二铵肠溶胶囊</span></s><s><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; color: #9a9a9a; mso-font-kerning: 0.00px;">3</span></s><s><span style="font-size: 13.30px; font-family: 宋体; mso-ascii-font-family: 'Segoe UI'; mso-hansi-font-family: 'Segoe UI'; mso-bidi-font-family: 'Segoe UI'; color: #9a9a9a; mso-font-kerning: 0.00px;">粒</span></s><s><span lang="EN-US" style="font-size: 13.30px; font-family: 'Segoe UI',sans-serif; mso-fareast-font-family: 宋体; color: #9a9a9a; mso-font-kerning: 0.00px;"> Tid<o:p></o:p></span></s><span class="s1">} ）</span><span class="s1">】</span></p></td></tr></tbody></table></div></div></div></div></div></div></div></div><br></div><p>&nbsp;</p><p><button id="paste-helper-id" style="width: 0px; height: 0px; padding: 0px; margin: 0px; border: 0px; display: none;"></button></p></div><!----></div><div class="translate-content-wrap" style="display: none;"><div tabindex="-1" class="cherry-editor-content tex2jax_ignore"><h3>翻译</h3>
      <br>
      <h3>标题: </h3>
      <br>
      </div><!----></div><div class="view-operator-area-wrapper"><div class="view-operator-area"><ul><li><i data-type="edit" class="tapd-icon-edit-v2"></i></li><!----><li><i class="tapd-icon-page-fullscreen-v2"></i></li></ul><span class="pack-up-button"><i class="tapd-icon-triangle-right-v2"></i></span></div></div></div><!----><!----></div><div data-v-82484c12="" class="el-dialog__wrapper" height="600px" style="display: none;"><div role="dialog" aria-modal="true" aria-label="显示设置" class="el-dialog" style="margin-top: 15vh; width: 810px;"><div class="el-dialog__header"><span class="el-dialog__title">显示设置</span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><div class="el-dialog__footer"><div data-v-82484c12="" class="dialog-footer"><button data-v-82484c12="" type="button" class="el-button el-button--primary"><!----><!----><span>确定</span></button><button data-v-82484c12="" type="button" class="el-button el-button--default"><!----><!----><span>取消</span></button></div></div></div></div></div><!----><div class="entity-detail-label detail-container-label" showtagadd="true"><h4 class="label-title">标签</h4><div class="tapd-tag-wrapper tapd-tag-wrapper--inline" show-tag-add="true"><button type="button" class="agi-button agi-button--text agi-button--level-secondary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-add-v2 agi-button__icon agi-button__left-icon"></i></button><div class="entity-card-tag-list"><span class="entity-card-tag__placeholder" style="display: none;"></span></div></div></div><!----><!----><div class="entity-detail-attachment detail-container-attachment"><div class="entity-detail-attachment-top-wrapper"><div class="entity-detail-attachment-top"><div class="entity-detail-attachment-top__expand-area"><i class="entity-detail-attachment-top__expand tapd-icon-triangle-down-v2" style=""></i><h4 class="title"> 附件 </h4></div><!----><div class="upload-content"><div class="agi-dropdown"><span class="agi-popover__reference"><div role="tooltip" id="el-popover-1284" aria-hidden="true" class="el-popover el-popper agi-popover agi-popover--info agi-popover--custom agi-dropdown__popover more-item-list" tabindex="0" style="display: none;"><!----></div><span class="el-popover__reference-wrapper"><button type="button" class="agi-button agi-button--text agi-button--level-secondary agi-button--size-small agi-button--icon-only el-popover__reference" aria-describedby="el-popover-1284" tabindex="0"><i class="agi-icon tapd-icon-add-v2 agi-button__icon agi-button__left-icon"></i></button></span></span></div><span class="drag-upload-action"><span>|</span><button type="button" class="drag-upload-action--button agi-button agi-button--text agi-button--level-secondary agi-button--size-small agi-button--text-only"><span class="agi-button__text"> 拖拽上传 </span></button></span></div><!----></div></div><div class="attachment-content-detail"><div class="t2-draggable"><div idx="0" class="draggable-item"><div class="attachment-content-detail__item"><ul><li><div class="thumb doc-icon-color"><a target="_blank" type="word-local" href="https://www.tapd.cn/22012671/attachments/preview_attachments/1122012671001000904/story?" file-name="侯某.docx" data-id="1122012671001000904" data-name="侯某.docx" file-type="word-local" class="gallery"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" alt="需求文档图片"></a></div><div class="title"><a target="_blank" file-name="侯某.docx" file-type="word-local" title="侯某.docx" href="https://www.tapd.cn/22012671/attachments/preview_attachments/1122012671001000904/story?" class="link-title"> 侯某.docx </a><div class="action"><a target="_blank" type="file" data-type="download" data-id="1122012671001000904" href="https://www.tapd.cn/22012671/attachments/download/1122012671001000904/story?" class="tapd-icon-download-v2"></a><!----><a data-id="1122012671001000904" data-name="侯某.docx" data-type="transfer" class="tapd-icon-save-as-v2"></a><a data-id="1122012671001000904" data-name="侯某.docx" data-deletetoken="c7286d319e4e2ad7891c29b278dd8911ef2ca518" data-type="delete" href="javascript:;" class="tapd-icon-trash-v2"></a><!----></div></div><div class="creator">郑鸣</div><div class="createtime">2023-07-17 10:33:38</div></li></ul></div></div></div></div><!----><!----><!----><!----><!----><!----><!----><div data-v-5fe71065=""><div data-v-5fe71065="" class="el-dialog__wrapper" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog agi-dialog agi-dialog--small " style="margin-top: 15vh;"><div class="el-dialog__header"><div class="agi-dialog__header"><span class="agi-dialog__title">下载</span><i class="agi-icon tapd-icon-close-v2 agi-dialog__close-icon"></i></div><!----></div><!----><div class="el-dialog__footer"><div class="agi-dialog__footer"><button type="button" class="agi-button agi-button--default agi-button--level-primary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">确认</span></button><button type="button" class="agi-button agi-button--plain agi-button--level-secondary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">取消</span></button></div></div></div></div></div><div class="el-dialog__wrapper" width="500px" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog agi-dialog agi-dialog--small agi-dialog--no-footer document-download-progress-dialog" style="margin-top: 15vh; width: 500px;"><div class="el-dialog__header"><div class="agi-dialog__header"><span class="agi-dialog__title">提示</span><i class="agi-icon tapd-icon-close-v2 agi-dialog__close-icon"></i></div><!----></div><!----><!----></div></div></div><!----><div class="entity-detail-comments detail-container-comments"><div class="entity-detail-comments-top-wrapper"><div class="entity-detail-comments-top"><div class="entity-detail-comments-top__expand-area"><i class="entity-detail-comments-top__expand tapd-icon-triangle-down-v2"></i><h4 class="title"> 评论 </h4></div><div class="add-content"><a class="add-comments"><i class="tapd-icon-add-v2"></i></a></div><span class="time-sort entity-detail-hover-area"><span> 按时间排序</span><i class="tapd-icon-sort-desc-v2"></i></span></div></div><div class="entity-detail-comments-area"><div commentindex="0" class="comment_content"><div class="avatar-container avatar-comments"><img alt="需求文档图片" title="" class="avatar-img-default common-avatar-img avatar" data-src="https://www.tapd.cn/20852831/users/avatar/30588581/jpg/0/small?t=244b417e8597892616ff" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" lazy="loaded"><!----></div><div class="rich-comment"><div class="comment-info"><span class="field-author">李若男6522</span><span class="field-active">在状态 [CODING] 添加</span></div><div class="editor-content comment_type_text webkit-scrollbar cherry-editor-content tex2jax_ignore bottom-comment"><div class="richText-comment-description"><p>分支名称前后端：caseWord,身高体重BMI 去掉，放在查体里面，最后word生成格式为姓+某+医院名称+序号。</p></div></div><div class="translate-content-wrap" style="display: none;"></div><div class="comment-actions" style="margin-top: 4px;"><span class="field-time">2023-08-09 10:24</span><span id="" class="threading view-comment"><span class="action"><i class="tapd-icon-message-v2"></i></span><!----><!----></span></div><!----><!----></div></div><div commentindex="1" class="comment_content"><div class="avatar-container avatar-comments"><!----><i title="" class="avatar-text-default avatar-h avatar"><span class="avatar-default-name">朱海</span></i></div><div class="rich-comment"><div class="comment-info"><span class="field-author">朱海华</span><span class="field-active">在状态 [CODING] 添加</span></div><div class="editor-content comment_type_text webkit-scrollbar cherry-editor-content tex2jax_ignore bottom-comment"><div class="richText-comment-description"><div class="tox-clear-float"><img title="点击看原图" href="https://file.tapd.cn//tfl/captures/2023-08/tapd_22012671_base64_1691049568_739.png" class="compress gallery" data-src="https://file.tapd.cn/compress/compress_img/700/tapd_22012671_base64_1691049568_739.png?src=/tfl/captures/2023-08/tapd_22012671_base64_1691049568_739.png" original_src="https://file.tapd.cn//tfl/captures/2023-08/tapd_22012671_base64_1691049568_739.png" style="max-width:80%;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" lazy="loaded"></div><p><span style="color: #182b50; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: #ffffff; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;">病历文件的命名，用 姓史+这种模糊形式的话，会导致很多人的病历都混在一起吧。加上有的人是同名的，感觉通过一个模糊的文件名就快速判断患者复诊次数的话。感觉不是很可靠</span></p></div></div><div class="translate-content-wrap" style="display: none;"></div><div class="comment-actions" style="margin-top: 4px;"><span class="field-time">2023-08-03 16:01</span><span id="" class="threading view-comment"><span class="action"><i class="tapd-icon-message-v2"></i></span><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-edit-v2 agi-button__icon agi-button__left-icon"></i></button><span popper-class="remove-confirm-popper" placement="left"><div role="tooltip" id="el-popover-9262" aria-hidden="true" class="el-popover el-popper remove-confirm-popper" tabindex="0" style="display: none;"><!----><div class="el-popconfirm"><p class="el-popconfirm__main"><i class="el-popconfirm__icon tapd-icon-info-round-fill-v2" style="color: rgb(255, 103, 112);"></i>
      确定删除该条评论吗？
    </p><div class="el-popconfirm__action"><button type="button" class="el-button el-button--text el-button--mini"><!----><!----><span>
        取消
      </span></button><button type="button" class="el-button el-button--danger el-button--mini"><!----><!----><span>
        确定
      </span></button></div></div></div><span class="el-popover__reference-wrapper"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only el-popover__reference" aria-describedby="el-popover-9262" tabindex="0"><i class="agi-icon tapd-icon-trash-v2 agi-button__icon agi-button__left-icon"></i></button></span></span></span></div><!----><!----></div></div><div commentindex="2" class="comment_content"><div class="avatar-container avatar-comments"><img alt="需求文档图片" title="" class="avatar-img-default common-avatar-img avatar" data-src="https://www.tapd.cn/20852831/users/avatar/30588581/jpg/0/small?t=244b417e8597892616ff" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" lazy="loaded"><!----></div><div class="rich-comment"><div class="comment-info"><span class="field-author">李若男6522</span><span class="field-active">在状态 [CODING] 添加</span></div><div class="editor-content comment_type_text webkit-scrollbar cherry-editor-content tex2jax_ignore bottom-comment"><div class="richText-comment-description"><p>三方包的使用1天，word读取，word生成1.5天。病历数据导入1天，zip包生成1.5天。权限以及ui1天<span>&nbsp;</span><b class="at-who" contenteditable="false" style="padding: 0px 8px; border-radius: 4px" data-userid="刘欢1226">@刘欢1226</b><span>&nbsp;</span></p></div></div><div class="translate-content-wrap" style="display: none;"></div><div class="comment-actions" style="margin-top: 4px;"><span class="field-time">2023-08-02 11:12</span><span id="" class="threading view-comment"><span class="action"><i class="tapd-icon-message-v2"></i></span><!----><!----></span></div><!----><!----></div></div><div commentindex="3" class="comment_content comment_content_last"><div class="avatar-container avatar-comments"><!----><i title="" class="avatar-text-default avatar-h avatar"><span class="avatar-default-name">杜曌</span></i></div><div class="rich-comment"><div class="comment-info"><span class="field-author">杜曌770386961</span><span class="field-active">在状态 [DESIGNING] 添加</span></div><div class="editor-content comment_type_text webkit-scrollbar cherry-editor-content tex2jax_ignore bottom-comment"><div class="richText-comment-description"><p>导出按钮做交互逻辑，点击后开始下载，在下载完成后，按钮重新可以点击</p></div></div><div class="translate-content-wrap" style="display: none;"></div><div class="comment-actions" style="margin-top: 4px;"><span class="field-time">2023-07-24 15:06</span><span id="" class="threading view-comment"><span class="action"><i class="tapd-icon-message-v2"></i></span><!----><!----></span></div><!----><!----></div></div><div class="entity-detail-comments-foldBtn"><!----><span class="foldBtn-wrap entity-detail-hover-area"><span class="foldBtn-text">收起更多</span><span class="tapd-icon-arrow-down-v2 arrow-down-v2-reverse foldBtn-icon"></span></span></div><div class="el-dialog__wrapper" style="display: none;"><div role="dialog" aria-modal="true" aria-label="添加评论" class="el-dialog comment-dialog" style="margin-top: 15vh; width: 896px;"><div class="el-dialog__header"><span class="el-dialog__title">添加评论</span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><div class="el-dialog__footer"><div class="dialog-footer comment-dialog-footer"><span class="notice" style="float: left; margin-top: 9px;">@通知他人，ctrl+enter快速提交</span><button type="button" class="el-button el-button--primary"><!----><!----><span>确定</span></button><button type="button" class="el-button el-button--default"><!----><!----><span>取消</span></button></div></div></div></div><!----><!----></div></div><div class="entity-detail-checklist expand"><!----><!----></div><div class="entity-status detail-container-status"><div class="entity-status__header"><span class="header-title">工作流</span><div style="display: inline-block;"><button type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small"><i class="agi-icon tapd-icon-workflow-setting-v2 agi-button__icon agi-button__left-icon"></i><span class="agi-button__text"> 查看流程图 </span></button><button type="button" class="status-workflow-action-group agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--icon-only"><i class="agi-icon tapd-icon-setting-v2 agi-button__icon agi-button__left-icon"></i></button></div></div><div class="entity-status__content"><div class="entity-status__content__diagram-wrapper"><!----><!----><!----><!----></div><div class="status-transfer-wrap status-transfer-wrap--top" afterchangetospecialstatus="function () { [native code] }"><div class="status-transfer status-transfer--top status-transfer--detail-mode" style="height: 520px;"><!----><!----><div class="el-tabs el-tabs--top"><div class="el-tabs__header is-top"><div class="el-tabs__nav-wrap is-top"><div class="el-tabs__nav-scroll"><div role="tablist" class="el-tabs__nav is-top" style="transform: translateX(0px);"><div class="el-tabs__active-bar is-top" style="width: 71px; transform: translateX(98px);"></div><div id="tab-0" aria-controls="pane-0" role="tab" tabindex="-1" class="el-tabs__item is-top is-disabled"><span class="el-tooltip tag-name" separator="" aria-describedby="el-tooltip-8556" tabindex="0"> ACCEPTING <!----></span><!----></div><div id="tab-1" aria-controls="pane-1" role="tab" aria-selected="true" tabindex="0" class="el-tabs__item is-top is-active"><!----><span slot="label" title="" class="tag-name"> DONE <span class="current-tag">(当前)</span></span></div></div></div></div></div><div class="el-tabs__content"><!----><!----><div width="630px" class="status-transition-wrapper" can-transition="true"><!----></div><div role="tabpanel" aria-hidden="true" id="pane-0" aria-labelledby="tab-0" class="el-tab-pane" style="display: none;"><form class="el-form transfer-form transfer-form--top transfer-form--detail-mode"><div class="transfer-form-content_div row-two-layout"><div class="el-tabs__form"><!----><!----><div class="transfer-form-wrap"><div class="transform-base-item"><div class="el-form-item custom-item" label-position="left"><label for="owner" class="el-form-item__label" style="width: 112px;"><span class="el-form-item__custom-label"><label>处理人</label><!----><span class="require">*</span></span></label><div class="el-form-item__content" style="margin-left: 112px;"><div><div class="tapd-user-select tapd-input" label="处理人" title="郑鸣;李若男6522;朱海华;杜曌770386961"><div class="el-input"><!----><input type="text" autocomplete="off" aria-label="处理人" title="" rows="1" placeholder="" class="el-input__inner"><!----><!----><!----><!----></div><!----></div></div><!----><!----></div></div><!----></div><!----><div><div class="el-form-item" style="display: none;"><!----><div class="el-form-item__content"><!----></div></div></div><div></div></div></div></div><div class="el-form-item is-action"><!----><div class="el-form-item__content"><div class="submit-button-group"><button type="button" class="agi-button agi-button--default agi-button--level-primary agi-button--size-small agi-button--text-only" id=""><span class="agi-button__text"> 流转 </span></button><!----></div><!----><!----><!----></div></div></form></div><div role="tabpanel" id="pane-1" aria-labelledby="tab-1" class="el-tab-pane" style=""><form class="el-form transfer-form transfer-form--top transfer-form--detail-mode"><div class="transfer-form-content_div row-two-layout"><div class="el-tabs__form"><!----><!----><div class="transfer-form-wrap"><div class="transform-base-item"><!----><div class="el-form-item custom-item" label-position="left"><label for="owner" class="el-form-item__label" style="width: 112px;"><span class="el-form-item__custom-label"><label>处理人</label><!----><span class="require">*</span></span></label><div class="el-form-item__content" style="margin-left: 112px;"><div><div class="tapd-user-select tapd-input" label="处理人" title="郑鸣;李若男6522;朱海华;杜曌770386961"><div class="el-input"><!----><input type="text" autocomplete="off" aria-label="处理人" title="" rows="1" placeholder="" class="el-input__inner"><!----><!----><!----><!----></div><!----></div></div><!----><!----></div></div></div><!----><div data-v-5433a9ff="" class="el-form-item comment-editor-wrapper" label-position="left" isparent="0"><label for="remarks" class="el-form-item__label" style="width: 112px;">评论</label><div class="el-form-item__content" style="margin-left: 112px;"><div data-v-5433a9ff="" class="tapd-editor comment-editor detail-model-comment-editor" id="comment-editor-1"><div class="tapd-richtext-editor"><div id="richtext-editor_28519175011748592841803" aria-hidden="true" style="display: none;"></div><div role="application" class="tox tox-tinymce" aria-disabled="false" style="visibility: hidden; width: 100%; height: 200px;"><div class="tox-editor-container"><div data-alloy-vertical-dir="toptobottom" class="tox-editor-header"><div role="group" class="tox-toolbar-overlord" aria-disabled="false"><div role="group" class="tox-toolbar__primary"><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="全屏" type="button" tabindex="-1" class="tox-tbtn" aria-disabled="false" aria-pressed="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-fullscreen-v2 editor-btn__fullscreen"></i></span></button><button aria-label="Markdown" type="button" tabindex="-1" class="tox-tbtn" aria-disabled="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-switchMarkdown-v2"></i></span></button><button aria-label="格式刷" type="button" tabindex="-1" class="tox-tbtn" aria-disabled="false" aria-pressed="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-format-v2"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="清除格式" type="button" tabindex="-1" class="tox-tbtn" aria-disabled="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-eraser-v2"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="" type="button" tabindex="-1" class="tox-tbtn tox-tbtn--disabled" aria-disabled="true"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-cherry-separator"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="插入" aria-haspopup="true" type="button" tabindex="-1" class="tox-tbtn tox-tbtn--select" aria-disabled="false" aria-expanded="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-add-round-v2"></i></span><span class="tox-tbtn__select-label">插入</span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="" type="button" tabindex="-1" class="tox-tbtn tox-tbtn--disabled" aria-disabled="true"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-cherry-separator"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="段落" aria-haspopup="true" type="button" unselectable="on" tabindex="-1" class="tox-tbtn tox-tbtn--select tox-tbtn--bespoke" aria-expanded="false" style="user-select: none;"><span class="tox-tbtn__select-label">正文</span><div class="tox-tbtn__select-chevron"><i class="editor-btn tapd-editor-icon tapd-editor-icon-arrow-down-v2"></i></div></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="字体" aria-haspopup="true" type="button" unselectable="on" tabindex="-1" class="tox-tbtn tox-tbtn--select tox-tbtn--bespoke" aria-expanded="false" style="user-select: none;"><span class="tox-tbtn__select-label">苹果苹方</span><div class="tox-tbtn__select-chevron"><i class="editor-btn tapd-editor-icon tapd-editor-icon-arrow-down-v2"></i></div></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="字号" aria-haspopup="true" type="button" unselectable="on" tabindex="-1" class="tox-tbtn tox-tbtn--select tox-tbtn--bespoke" aria-expanded="false" style="user-select: none;"><span class="tox-tbtn__select-label">12px</span><div class="tox-tbtn__select-chevron"><i class="editor-btn tapd-editor-icon tapd-editor-icon-arrow-down-v2"></i></div></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="" type="button" tabindex="-1" class="tox-tbtn tox-tbtn--disabled" aria-disabled="true"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-cherry-separator"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="粗体" type="button" tabindex="-1" class="tox-tbtn" aria-disabled="false" aria-pressed="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-bold-v2"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="斜体" type="button" tabindex="-1" class="tox-tbtn" aria-disabled="false" aria-pressed="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-italic-v2"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="下划线" type="button" tabindex="-1" class="tox-tbtn" aria-disabled="false" aria-pressed="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-underline-v2"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="删除线" type="button" tabindex="-1" class="tox-tbtn" aria-disabled="false" aria-pressed="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-deleteline-v2"></i></span></button></div><div title="" role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="" type="button" tabindex="-1" class="tox-tbtn tox-tbtn--disabled" aria-disabled="true"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-cherry-separator"></i></span></button></div><div role="toolbar" data-alloy-tabstop="true" tabindex="-1" class="tox-toolbar__group"><button aria-label="更多" aria-haspopup="true" type="button" data-alloy-tabstop="true" tabindex="-1" class="tox-tbtn" aria-expanded="false"><span class="tox-icon tox-tbtn__icon-wrap"><i class="editor-btn tapd-editor-icon tapd-editor-icon-omit-v2"></i></span></button></div></div></div><div class="tox-anchorbar"></div></div><div class="tox-sidebar-wrap"><div class="tox-edit-area"><iframe id="richtext-editor_28519175011748592841803_ifr" frameborder="0" allowtransparency="true" title="编辑区. 按Alt+0键打开帮助" class="tox-edit-area__iframe"></iframe></div><div role="complementary" class="tox-sidebar"><div data-alloy-tabstop="true" tabindex="-1" class="tox-sidebar__slider tox-sidebar--sliding-closed" style="width: 0px;"><div class="tox-sidebar__pane-container"></div></div></div></div></div><div aria-hidden="true" class="tox-throbber" style="display: none;"></div></div><input type="hidden" name="richtext-editor_28519175011748592841803" value=""> <div class="right-toolbar" style="display: none;"><span title="Markdown" class="toolbar-btn"><span class="toolbar-btn-text" style="display: none;">Markdown</span> <i class="tapd-editor-icon tapd-editor-icon-switchMarkdown-v2"></i></span> <span title="全屏编辑" class="toolbar-btn"><i class="tapd-editor-icon tapd-icon-page-fullscreen-v2"></i></span></div></div><div class="el-loading-mask" style="display: none;"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg><!----></div></div></div><div data-v-5433a9ff="" class="comment-actions" style="display: none;"><button data-v-5433a9ff="" type="button" class="agi-button agi-button--text agi-button--level-tertiary agi-button--size-small agi-button--text-only"><span class="agi-button__text"> Aa富文本格式 </span></button></div><!----><!----><!----></div></div><div></div></div></div></div><div class="el-form-item is-action"><!----><div class="el-form-item__content"><div class="submit-button-group"><button type="button" class="agi-button agi-button--default agi-button--level-primary agi-button--size-small agi-button--text-only" id="guide-trans-btn"><span class="agi-button__text"> 流转 </span></button><!----></div><!----><!----><!----></div></div></form></div></div></div></div><!----><div class="el-loading-mask" style="display: none;"><div class="el-loading-spinner default-spinner"><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" class="agi-loading agi-loading--medium"><linearGradient id="agi-loading__medium-gradient-right-epjw0j2ws7j" gradientUnits="userSpaceOnUse" x1="20" y1="12" x2="20" y2="28"><stop offset="0" style="stop-color: rgb(53, 130, 251); stop-opacity: 1;"></stop><stop offset="1" style="stop-color: rgb(53, 130, 251); stop-opacity: 0.5;"></stop></linearGradient><path fill-rule="evenodd" clip-rule="evenodd" d="M16 2C8.26801 2 2 8.26801 2 16C2 23.732 8.26801 30 16 30V26C10.4772 26 6 21.5228 6 16C6 10.4772 10.4772 6 16 6V2Z" fill="url(#agi-loading__medium-gradient-right-epjw0j2ws7j)" class="agi-loading__path-right"></path><linearGradient id="agi-loading__medium-gradient-left-epjw0j2ws7j" gradientUnits="userSpaceOnUse" x1="4" y1="2" x2="4" y2="28"><stop offset="0" style="stop-color: rgb(53, 130, 251); stop-opacity: 0;"></stop><stop offset="1" style="stop-color: rgb(53, 130, 251); stop-opacity: 0.5;"></stop></linearGradient><path fill-rule="evenodd" clip-rule="evenodd" d="M16 2C23.732 2 30 8.26801 30 16C30 23.732 23.732 30 16 30V26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6V2Z" fill="url(#agi-loading__medium-gradient-left-epjw0j2ws7j)" class="agi-loading__path-left"></path><circle cx="16" cy="4" r="2" fill="#3582FB" class="top"></circle></svg><!----></div></div></div></div></div></div><div class="comment-create-area detail-container-bottom-comment comment-create-area--folded"><div class="comment-create-area__left"><span class="t2-avatar t2-avatar--medium" workspace_id="22012671" style="background-image: url(&quot;https://www.tapd.cn/22012671/users/avatar/朱海华&quot;), url(&quot;https://www.tapd.cn/img/avatar.jpg&quot;); border-color: rgb(237, 238, 241);"></span></div><div class="comment-create-area__right"><div class="comment-editor-placeholder"> 输入评论，@通知他人，ctrl + enter提交 </div></div></div></div><div class="devide-line"></div><div class="detail-container-right webkit-scrollbar"><div data-v-0c2abd8c="" class="entity-detail-right" customeditortoolbar="" entitystatus="" disabledmodules="" apicontrollerprefix="" apiadditionparams="[object Object]" ispage="true" defaultbaseinfo="[object Object]" mainlocation="/prong/stories/stories_list" fromiterationid="" menuworkitemtypeid="0" parentiteminfo="" inited="1"><div data-v-0c2abd8c="" class="entity-detail-right__top webkit-scrollbar"><div data-v-0c2abd8c="" class="entity-detail-right-info"><span data-v-0c2abd8c="">基础信息</span></div><div data-v-0c2abd8c="" class="entity-detail-right-col-wrapper entity-detail-right-col-wrapper--shrink"><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-select-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">状态</span></div><div data-v-0c2abd8c="" field-name="status" class="entity-detail-right-col__value editable-value entity-detail-right-col__status-value"><div class="tapd-priority-select__label "><button type="button" class="el-button el-button--default capsule light-editable capsule--end" id="1122012671001003497" html_type="status" value="DONE" field="status" title="DONE"><!----><!----><span>DONE</span></button></div></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-select-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">分类</span></div><div data-v-0c2abd8c="" field-name="category_id" class="entity-detail-right-col__value editable-value"><span class="light-editable  " disabledtips="" title="未分类" id="1122012671001003497" field="category_id" html_type="select" select-with-color="0" value="-1" href="" target="_blank">未分类</span><i class="label-selectable__icon tapd-icon-arrow-down-v2 render-list-icon"></i></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-iterations-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">迭代</span></div><div data-v-0c2abd8c="" field-name="iteration_id" class="entity-detail-right-col__value editable-value entity-detail-right-col__value--double-hot-zone"><span class="light-editable  light-editable--link" disabledtips="" title="web-2023.7" id="1122012671001003497" field="iteration_id" html_type="select" select-with-color="0" value="1122012671001000196" href="https://www.tapd.cn/22012671/prong/iterations/card_view/1122012671001000196" target="_blank">web-2023.7</span><i class="label-selectable__icon tapd-icon-arrow-down-v2 render-list-icon"></i></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-fast-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">优先级</span></div><div data-v-0c2abd8c="" field-name="priority" class="entity-detail-right-col__value editable-value"><div id="1122012671001003497" field="priority" title="Middle" class="tapd-priority-label-v2 colorful-labels light-editable"><span class="colorful-labels__item" style="color: rgb(255, 255, 255); background-color: rgb(40, 171, 128); border: 1px solid rgba(40, 171, 128, 0.06);"><span class="colorful-labels__item-text">Middle</span></span></div></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-user-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">处理人</span></div><div data-v-0c2abd8c="" field-name="owner" class="entity-detail-right-col__value editable-value"><span class="light-editable  " disabledtips="" workspace_id="22012671" title_username="" id="1122012671001003497" field="owner" html_type="user_chooser" select-with-color="0" value="郑鸣;李若男6522;朱海华;杜曌770386961" href="" target="_blank">郑鸣;李若男6522;朱海华;杜曌770386961</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-user-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">开发人员</span></div><div data-v-0c2abd8c="" field-name="developer" class="entity-detail-right-col__value editable-value"><span class="light-editable  " disabledtips="" title="李若男6522;" workspace_id="22012671" title_username="" id="1122012671001003497" field="developer" html_type="user_chooser" select-with-color="0" value="李若男6522;" href="" target="_blank">李若男6522;</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-date-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">预计开始</span></div><div data-v-0c2abd8c="" field-name="begin" class="entity-detail-right-col__value editable-value"><span class="light-editable  " disabledtips="" title="2023-08-02" id="1122012671001003497" field="begin" html_type="dateinput" select-with-color="0" value="2023-08-02" href="" target="_blank">2023-08-02</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-date-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">预计结束</span></div><div data-v-0c2abd8c="" field-name="due" class="entity-detail-right-col__value editable-value"><span class="light-editable  " disabledtips="" title="2023-08-09" id="1122012671001003497" field="due" html_type="dateinput" select-with-color="0" value="2023-08-09" href="" target="_blank">2023-08-09</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-date-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">测试开始日期</span></div><div data-v-0c2abd8c="" field-name="custom_field_one" class="entity-detail-right-col__value editable-value"><span class="light-editable  " disabledtips="" title="--" id="1122012671001003497" field="custom_field_one" html_type="dateinput" select-with-color="0" value="" href="" target="_blank">--</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-date-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">测试结束日期</span></div><div data-v-0c2abd8c="" field-name="custom_field_two" class="entity-detail-right-col__value editable-value"><span class="light-editable  " disabledtips="" title="--" id="1122012671001003497" field="custom_field_two" html_type="dateinput" select-with-color="0" value="" href="" target="_blank">--</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-father-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">父需求</span></div><div data-v-0c2abd8c="" class="entity-detail-right-col__value entity-detail-right-col__workitem-value editable-value"><div data-v-0c2abd8c="" class="inline-choose-workitem__wrapper"><!----><div class="inline-choose-workitem__selected"><span class="inline-choose-workitem__selected-label"><a href="https://www.tapd.cn/tapd_fe/22012671/story/detail/1122012671001003539" title="【web端(新增)】单品销售数据" target="_blank">【web端(新增)】单品销售数据</a><span class="inline-choose-workitem__selected-icon"><i class="tapd-icon-search"></i><i class="tapd-icon-close"></i></span></span></div><!----><!----><!----></div></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-user-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">创建人</span></div><div data-v-0c2abd8c="" field-name="creator" class="entity-detail-right-col__value"><span class="  inline-readonly " disabledtips="" workspace_id="22012671" title_username="" id="1122012671001003497" field="creator" html_type="user_chooser" select-with-color="0" value="郑鸣" href="" target="_blank">郑鸣</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-date-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">创建时间</span></div><div data-v-0c2abd8c="" field-name="created" class="entity-detail-right-col__value"><span class="  inline-readonly " disabledtips="" title="2023-07-12 17:42:43" id="1122012671001003497" field="created" html_type="datetime" select-with-color="0" value="2023-07-12 17:42:43" href="" target="_blank">2023-07-12 17:42:43</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-date-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">完成时间</span></div><div data-v-0c2abd8c="" field-name="completed" class="entity-detail-right-col__value"><span class="  inline-readonly " disabledtips="" title="2023-08-25 10:21:41" id="1122012671001003497" field="completed" html_type="datetime" select-with-color="0" value="2023-08-25 10:21:41" href="" target="_blank">2023-08-25 10:21:41</span></div></div><div data-v-0c2abd8c="" class="entity-detail-right-col"><div data-v-0c2abd8c="" class="entity-detail-right-col__key"><i data-v-0c2abd8c="" class="tapd-icon-user-v2 entity-detail-right-col__icon"></i><span data-v-0c2abd8c="">测试人员</span></div><div data-v-0c2abd8c="" field-name="custom_field_three" class="entity-detail-right-col__value editable-value"><span class="light-editable  " disabledtips="" title="--" workspace_id="22012671" title_username="" id="1122012671001003497" field="custom_field_three" html_type="user_chooser" select-with-color="0" value="" href="" target="_blank">--</span></div></div></div><div data-v-0c2abd8c="" class="el-dialog__wrapper fields-dialog" height="600px" width="910px" append-to-body="true" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog agi-dialog agi-dialog--small setting-fields-dialog" style="margin-top: 15vh; width: 910px;"><div class="el-dialog__header"><div class="agi-dialog__header"><span class="agi-dialog__title">显示字段设置</span><i class="agi-icon tapd-icon-close-v2 agi-dialog__close-icon"></i></div><!----></div><!----><div class="el-dialog__footer"><div class="dialog-footer"><button type="button" class="agi-button agi-button--default agi-button--level-primary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">确定</span></button><button type="button" class="agi-button agi-button--plain agi-button--level-secondary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">保存并同步到所有状态</span></button><button type="button" class="agi-button agi-button--plain agi-button--level-secondary agi-button--size-medium agi-button--text-only"><span class="agi-button__text"> 取消 </span></button></div></div></div></div><div data-v-9fcc3140="" data-v-0c2abd8c=""><div data-v-9fcc3140="" class="editable-wrapper" style="top: 0px; left: 0px; width: 0px; height: 0px; display: none;"><div data-v-9fcc3140="" class="tapd-inline-input" name="" inlinestatus="editing"><div title="" class="tapd-inline-wrap"><div class="tapd-inline-wrap__textwrapper"><div class="tapd-inline-wrap__text"><div title="--" class="tapd-inline-label-selectable"><p class="label-selectable__tag"> -- <!----></p></div></div></div><!----><!----></div></div><!----><!----><!----><!----></div></div></div><div data-v-0c2abd8c="" class="entity-detail-right__bottom"></div></div><!----></div></div><!----><!----><!----></div><div class="el-dialog__wrapper apply-reason-dialog" width="400px" style="display: none;"><div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog agi-dialog agi-dialog--small " style="margin-top: 15vh; width: 400px;"><div class="el-dialog__header"><div class="agi-dialog__header"><span class="agi-dialog__title">申请理由</span><i class="agi-icon tapd-icon-close-v2 agi-dialog__close-icon"></i></div><!----></div><!----><div class="el-dialog__footer"><span class="dialog-footer"><button type="button" class="el-button el-button--primary"><!----><!----><span> 提交 </span></button><button type="button" class="el-button el-button--default"><!----><!----><span> 取消 </span></button></span></div></div></div><!----></div></div></div><!----></div></div><!----><!----><div class="tapd-api-update-notice"></div><span class="flash-message-container"></span><!----></div><!----><div><!----><svg id="guideSvg" xmlns="http://www.w3.org/2000/svg" style="position: absolute; z-index: 2002; pointer-events: none; display: none;"><defs><marker id="arrow" markerWidth="5" markerHeight="5" refX="4.5" refY="2.5" orient="auto"><path d="M0,0 L5,2.5 L0,5 Z" fill="#0D68FF"></path></marker></defs><path id="guidePath" fill="none" stroke="#0D68FF" stroke-width="2" stroke-dasharray="3,3" marker-end="url(#arrow)"></path></svg></div><div data-v-6863707b="" class="wrapper operation-notice-card" style="right: 20px; display: none;"><div data-v-6863707b="" class="header"><img data-v-6863707b="" width="344" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" alt="需求文档图片"><div data-v-6863707b="" class="close-btn"><img data-v-6863707b="" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxHgA6+8BfVCwEQAAAABJRU5ErkJggg==" alt="需求文档图片"></div></div><div data-v-6863707b="" class="main tapd-scrollbar"><h3 data-v-6863707b="" class="mn-title">--</h3><div data-v-6863707b="" class="mn-content"></div></div><div data-v-6863707b="" class="footer" style="display: none;"><button data-v-6863707b="" type="button" class="agi-button agi-button--default agi-button--level-primary agi-button--size-medium agi-button--text-only"><span class="agi-button__text">  </span></button></div></div></div><script>window.IS_IN_UE_ROUTE = location.pathname.indexOf('/ue/') > -1;
    window.ENABLE_NEW_DETAIL = false;
    window.HideLeftOrTopOrBoth = location.search.indexOf('hidden_left_side=true') > -1 || location.search.indexOf('hidden_top_side=true') > -1 || location.search.indexOf('only_content=true') > -1
    window.fromMiniprogramThirdtools = sessionStorage.getItem('from_miniprogram_thirdtools') == 1 || location.search.indexOf('from_miniprogram_thirdtools') > -1;
    function checkIsNeedSkeleton() {
      // ue 一级路由下需屏蔽预览占位
      if (window.IS_IN_UE_ROUTE) {
        return false
      }
      if( window.HideLeftOrTopOrBoth ) {
        return false
      }
      if (window.fromMiniprogramThirdtools) {
        return false;
      }
      return true
    }

    var isNeedSkeleton = checkIsNeedSkeleton();
    if (isNeedSkeleton) {
      showSkeleton();
    }

    function showSkeleton() {
      var leftTreeInfo = localStorage.getItem('left_tree_info');
      var navigationInfos = localStorage.getItem('navigationInfos');
      var projectVersion = sessionStorage.getItem('project-version');
      
      if (navigationInfos) {
        navigationInfos = JSON.parse(navigationInfos);
      }
      var leftNavigationWrap;
      var topNavigationWrap;
      if (leftTreeInfo) {
        leftNavigationWrap = document.querySelector('.skeleton-content__v1');
        leftTreeInfo = JSON.parse(leftTreeInfo);
        
        if (leftTreeInfo.isLight) {
          leftNavigationWrap.classList.add('light');
        } else {
          leftNavigationWrap.classList.add('dark');
        }
        if (leftTreeInfo.isWide) {
          leftNavigationWrap.querySelector('.left-tree-unfold').style.display = '';
          if (projectVersion === 'program') {
            leftNavigationWrap.querySelector('.program-skeleton-navigation').style.display = '';
          } else {
            leftNavigationWrap.querySelector('.product-skeleton-navigation').style.display = '';
          }
          topNavigationWrap = leftNavigationWrap.querySelector('.left-tree-unfold .skeleton-navigation');
        } else {
          leftNavigationWrap.querySelector('.left-tree-fold').style.display = '';
          if (projectVersion === 'program') {
            leftNavigationWrap.querySelector('.program-skeleton-navigation').style.display = '';
          } else {
            leftNavigationWrap.querySelector('.product-skeleton-navigation').style.display = '';
          }
          topNavigationWrap = leftNavigationWrap.querySelector('.left-tree-fold .skeleton-navigation');
        }
      } else {
        if (navigationInfos && !navigationInfos.isNewLeftNav) {
          leftNavigationWrap = document.querySelector('.skeleton-content__v0');
          topNavigationWrap = leftNavigationWrap.querySelector('.skeleton-navigation');
        }
      }
      if (leftNavigationWrap) {
        if (navigationInfos && navigationInfos.noNavigationModule) {
          if (navigationInfos.noNavigationModule.some(
            moduleName => location.href.indexOf(moduleName) > -1,
          )) {
            if (topNavigationWrap) {
              topNavigationWrap.style.display = 'none';
            }
          }
        }
        leftNavigationWrap.style.display = '';
      }
    }

    document.open();
    
    document.write("<script src='/js/compress/locale/" + userLocale + ".js?version=" + version + "'><\/script>");
    
    
      document.write("<script src='/js/keep_url_params_in_frame.js'><\/script>");
        
    document.close();</script><script src="/js/compress/locale/zh_CN.js?version=1748511424853"></script><script src="/js/keep_url_params_in_frame.js"></script><canvas id="publicCanvas" style="height: 0px;width: 0px;position: absolute;z-index: -9999;"></canvas><script src="//static-fe.tapd.cn/js/vendor.dll.608ae94ab7155540d173.js" onerror="tapdFeResourceOnerror(event)"></script><script src="//static-fe.tapd.cn/runtime.da9b42823ff5.js" onerror="tapdFeResourceOnerror(event)"></script><script src="//static-fe.tapd.cn/chunk-agile.19dc53df2298.js" onerror="tapdFeResourceOnerror(event)"></script><script src="//static-fe.tapd.cn/chunk-tapdui.e6e18ffbe5a1.js" onerror="tapdFeResourceOnerror(event)"></script><script src="//static-fe.tapd.cn/chunk-vendors.52137e65a791.js" onerror="tapdFeResourceOnerror(event)"></script><script src="//static-fe.tapd.cn/chunk-common.9334e8f66aa5.js" onerror="tapdFeResourceOnerror(event)"></script><script src="//static-fe.tapd.cn/fe.25083a5bf67b.js" onerror="tapdFeResourceOnerror(event)"></script><div id="mbGetImgModule" class="mb-getmaterial-box" style="display: block;">
		               <div class="wzy-dialog"></div>
		               <div class="btn-group" data-src="https://file.tapd.cn/compress/compress_img/1400/tapd_22012671_base64_1689157053_454.png?src=/tfl/captures/2023-07/tapd_22012671_base64_1689157053_454.png" style="top: -106.016px; left: 241.997px; display: none;">
		                  <div class="btn-group-body">
		                     <span>采集</span>
		                     <svg class="wzyiconxiala" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path fill="#ffffff" d="M732.16000001 431.104L535.04 645.12c-6.656 6.656-13.312 9.728-23.04 9.728s-16.384-3.072-23.04-9.728L291.83999999 431.104c-9.728-9.728-13.312-26.112-6.65599999-39.424 6.656-13.31200001 16.384-23.04 29.696-23.04l394.752 0c13.312 0 26.112 9.728 29.696 23.04 6.14399999 13.312 3.072 29.696-7.16799999 39.424z"></path></svg>
		                  </div>
		                  <div class="mb-getmaterial-dropdown" style="display: none;">
							<ul class="mb-getmaterial-medias"><li data-ghid="mb" data-ghidname="美编素材库" class="collect-active"><svg t="1667288501413" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2556" width="200" height="200"><path d="M219.952 512.576l210.432 210.432-45.248 45.256-210.432-210.432z" p-id="2557"></path><path d="M799.672 262.264l45.256 45.256-460.464 460.464-45.256-45.256z" p-id="2558"></path></svg>美编素材库</li></ul>
							<div>编辑</div>
							<ul class="mb-getmaterial-opts">
								<li class="mb-getmaterial-add-btn"><svg t="1667288501413" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2556" width="200" height="200"><path d="M219.952 512.576l210.432 210.432-45.248 45.256-210.432-210.432z" p-id="2557"></path><path d="M799.672 262.264l45.256 45.256-460.464 460.464-45.256-45.256z" p-id="2558"></path></svg>添加新的媒体</li>
								<li class="mb-getmaterial-hide-btn"><svg t="1667288501413" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2556" width="200" height="200"><path d="M219.952 512.576l210.432 210.432-45.248 45.256-210.432-210.432z" p-id="2557"></path><path d="M799.672 262.264l45.256 45.256-460.464 460.464-45.256-45.256z" p-id="2558"></path></svg>隐藏采集图片</li>
							</ul> 
						  </div>
		               </div>
					</div>
					<div id="mbCollectedMp">
						<div class="mb-collected-mp">
							<h3>选择采集的公众号</h3>
							<ul class="mb-collected-mps"><li data-ghid="mb" data-ghidname="美编素材库" class="collect-active"><span></span>美编素材库</li></ul>
							<div class="mb-collected-mp-footer">
								<div class="mb-collected-mp-footer-cancel">取 消</div>
								<div class="mb-collected-mp-footer-enter">确 定</div>
							</div>
						</div>
					</div>
				  <div id="glmos-main-content" class="glmos-main-content" data-version="1.0.27"></div><link rel="stylesheet" href="chrome-extension://mnpdbmgpebfihcndnpgdaihnkmloclkd/do-action-pointer.css"><div class="tapd-knowledge-card" style="top: 0px; left: 0px; bottom: 0px; display: none;"><div class="tapd-knowledge-card_title"></div><div class="tapd-knowledge-card_content webkit-scrollbar"></div><!----></div><!----><div class="entity-card-tag-panel" style="display: none;"><div class="entity-card-tag-top"><div class="entity-card-tag-input"><div class="el-input"><!----><input type="text" autocomplete="off" placeholder="搜索或新建标签" maxlength="15" class="el-input__inner"><!----><!----><!----><!----></div><span class="tapd-icon-close" style="display: none;"></span></div></div><div class="entity-card-tag-candidate tapd-scrollbar" style="height: 32px; display: none;"><div class="entity-card-tag-item-head">最近输入</div></div><hr class="entity-card-tag-hr" style="display: none;"><div class="entity-card-tag-candidate tapd-scrollbar" style="height: 192px;"><div class="entity-card-tag-item" style="background-color: rgb(255, 255, 255);"><div class="entity-card-tag-color"><div class="color-box" style="background-color: rgb(255, 103, 112);"></div></div><div class="entity-card-tag-name">阻塞</div></div><div class="entity-card-tag-item" style="background-color: rgb(255, 255, 255);"><div class="entity-card-tag-color"><div class="color-box" style="background-color: rgb(255, 103, 112);"></div></div><div class="entity-card-tag-name">开发受阻</div></div><div class="entity-card-tag-item" style="background-color: rgb(255, 255, 255);"><div class="entity-card-tag-color"><div class="color-box" style="background-color: rgb(255, 103, 112);"></div></div><div class="entity-card-tag-name">有风险</div></div><div class="entity-card-tag-item" style="background-color: rgb(255, 255, 255);"><div class="entity-card-tag-color"><div class="color-box" style="background-color: rgb(40, 171, 128);"></div></div><div class="entity-card-tag-name">等待设计走查</div></div><div class="entity-card-tag-item" style="background-color: rgb(255, 255, 255);"><div class="entity-card-tag-color"><div class="color-box" style="background-color: rgb(40, 171, 128);"></div></div><div class="entity-card-tag-name">方案已沟通</div></div><div class="entity-card-tag-item" style="background-color: rgb(255, 255, 255);"><div class="entity-card-tag-color"><div class="color-box" style="background-color: rgb(40, 171, 128);"></div></div><div class="entity-card-tag-name">等待转测</div></div></div><hr class="entity-card-tag-hr" style="display: none;"><div class="entity-card-tag-add" style="display: none;"><div class="entity-card-tag-name"><i class="tapd-icon-add-v2"></i><span>新建 [</span><span class="entity-card-tag-name__text"></span><span>] 标签</span></div></div><div class="entity-card-tag-add entity-card-tag-setting" style="display: none;"><div class="entity-card-tag-name"><i class="tapd-icon-setting-v2"></i><span class="label-management"> 标签管理</span></div></div></div><div class="el-dialog__wrapper story-dialog" style="display: none;"><div role="dialog" aria-modal="true" aria-label="修改父需求" class="el-dialog" style="margin-top: 15vh; width: 900px;"><div class="el-dialog__header"><span class="el-dialog__title">修改父需求</span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><!----></div></div><link href="https://tdl.tapd.cn/tfl/js/cherry-richtext/cherry/skins/ui/oxide/skin.min.css" rel="stylesheet"><script type="text/javascript" src="https://tdl.tapd.cn/tfl/js/cherry-richtext/cherry/cherry-editor.min.js"></script><script type="text/javascript" src="https://tdl.tapd.cn/tfl/js/cherry-richtext/cherry/plugins/cherry-plugins/plugin.js"></script><div class="tox tox-silver-sink tox-tinymce-aux" style="position: relative;"></div><script type="text/javascript" src="//static-fe.tapd.cn/externals_shared/logic/math-jax.js"></script><script type="text/javascript" src="//static-fe.tapd.cn/externals_shared/ext-libs/mathjax3/mathjax/tex-mml-svg.js"></script><!----><div role="tooltip" id="el-tooltip-5109" aria-hidden="true" class="el-tooltip__popper is-dark agi-tooltip agi-tooltip--not-enterable" style="transform-origin: center bottom; z-index: 2009; display: none;">点击复制 ID<div x-arrow="" class="popper__arrow" style="left: 53.5px;"></div></div>
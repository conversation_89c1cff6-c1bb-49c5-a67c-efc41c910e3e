<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>TAPD插件连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TAPD插件连接测试工具</h1>
        
        <div class="test-section">
            <h3>📡 服务器连接测试</h3>
            <input type="text" id="serverUrl" placeholder="http://服务器IP:9999" value="http://************:9999">
            <button onclick="testServerConnection()">测试基础连接</button>
            <button onclick="testCORS()">测试CORS配置</button>
            <button onclick="testAPI()">测试API接口</button>
            <div id="connectionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🌐 网络诊断</h3>
            <button onclick="testDNS()">DNS解析测试</button>
            <button onclick="testPing()">连通性测试</button>
            <button onclick="getNetworkInfo()">网络信息</button>
            <div id="networkResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🍪 Cookie测试</h3>
            <button onclick="testCookies()">检查TAPD Cookie</button>
            <button onclick="clearCookies()">清除测试Cookie</button>
            <div id="cookieResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 完整API测试</h3>
            <button onclick="testFullAPI()">完整流程测试</button>
            <div id="fullTestResult" class="result"></div>
        </div>
    </div>

    <script>
        // 获取服务器URL
        function getServerUrl() {
            return document.getElementById('serverUrl').value.trim();
        }

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 测试服务器基础连接
        async function testServerConnection() {
            const serverUrl = getServerUrl();
            showResult('connectionResult', '正在测试服务器连接...', 'info');
            
            try {
                const response = await fetch(serverUrl + '/', {
                    method: 'GET',
                    mode: 'cors',
                    credentials: 'omit'
                });
                
                if (response.ok) {
                    showResult('connectionResult', `✅ 服务器连接成功\n状态码: ${response.status}\n响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`, 'success');
                } else {
                    showResult('connectionResult', `⚠️ 服务器响应异常\n状态码: ${response.status}\n状态文本: ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult('connectionResult', `❌ 连接失败\n错误: ${error.message}\n类型: ${error.constructor.name}`, 'error');
            }
        }

        // 测试CORS配置
        async function testCORS() {
            const serverUrl = getServerUrl();
            showResult('connectionResult', '正在测试CORS配置...', 'info');
            
            try {
                const response = await fetch(serverUrl + '/api/v1/reqAgent/tapd/parse', {
                    method: 'OPTIONS',
                    mode: 'cors',
                    headers: {
                        'Origin': 'chrome-extension://test',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                const corsHeaders = {};
                response.headers.forEach((value, key) => {
                    if (key.toLowerCase().includes('access-control')) {
                        corsHeaders[key] = value;
                    }
                });
                
                showResult('connectionResult', `✅ CORS预检请求成功\n状态码: ${response.status}\nCORS头部: ${JSON.stringify(corsHeaders, null, 2)}`, 'success');
            } catch (error) {
                showResult('connectionResult', `❌ CORS测试失败\n错误: ${error.message}`, 'error');
            }
        }

        // 测试API接口
        async function testAPI() {
            const serverUrl = getServerUrl();
            showResult('connectionResult', '正在测试API接口...', 'info');
            
            const testData = {
                url: "https://www.tapd.cn/test/story/detail/123456",
                title: "测试需求标题",
                content: "<div>测试需求内容</div>",
                cookies: "test_cookie=test_value",
                user_id: 1,
                project_id: 2
            };
            
            try {
                const response = await fetch(serverUrl + '/api/v1/reqAgent/tapd/parse', {
                    method: 'POST',
                    mode: 'cors',
                    credentials: 'omit',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'chrome-extension://test'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showResult('connectionResult', `✅ API测试成功\n响应: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult('connectionResult', `❌ API测试失败\n状态码: ${response.status}\n错误: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult('connectionResult', `❌ API请求失败\n错误: ${error.message}`, 'error');
            }
        }

        // 测试DNS解析
        async function testDNS() {
            showResult('networkResult', '正在进行DNS解析测试...', 'info');
            
            try {
                const serverUrl = getServerUrl();
                const url = new URL(serverUrl);
                const hostname = url.hostname;
                
                // 简单的DNS测试 - 尝试连接
                const startTime = Date.now();
                const response = await fetch(`http://${hostname}:${url.port || 80}/`, {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                const endTime = Date.now();
                
                showResult('networkResult', `✅ DNS解析成功\n主机: ${hostname}\n响应时间: ${endTime - startTime}ms`, 'success');
            } catch (error) {
                showResult('networkResult', `❌ DNS解析失败\n错误: ${error.message}`, 'error');
            }
        }

        // 测试连通性
        async function testPing() {
            showResult('networkResult', '正在测试连通性...', 'info');
            
            try {
                const serverUrl = getServerUrl();
                const startTime = Date.now();
                
                const response = await fetch(serverUrl + '/', {
                    method: 'HEAD',
                    mode: 'cors',
                    credentials: 'omit'
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                showResult('networkResult', `✅ 连通性测试成功\n响应时间: ${responseTime}ms\n状态: ${response.status}`, 'success');
            } catch (error) {
                showResult('networkResult', `❌ 连通性测试失败\n错误: ${error.message}`, 'error');
            }
        }

        // 获取网络信息
        async function getNetworkInfo() {
            showResult('networkResult', '正在获取网络信息...', 'info');
            
            const info = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                onLine: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled,
                currentUrl: window.location.href,
                timestamp: new Date().toISOString()
            };
            
            showResult('networkResult', `📊 网络信息:\n${JSON.stringify(info, null, 2)}`, 'info');
        }

        // 测试Cookie
        async function testCookies() {
            showResult('cookieResult', '正在检查TAPD Cookie...', 'info');
            
            if (typeof chrome !== 'undefined' && chrome.cookies) {
                try {
                    chrome.cookies.getAll({domain: 'tapd.cn'}, function(cookies) {
                        const cookieInfo = cookies.map(cookie => ({
                            name: cookie.name,
                            value: cookie.value.substring(0, 20) + '...',
                            domain: cookie.domain,
                            path: cookie.path
                        }));
                        
                        showResult('cookieResult', `🍪 找到 ${cookies.length} 个TAPD Cookie:\n${JSON.stringify(cookieInfo, null, 2)}`, 'success');
                    });
                } catch (error) {
                    showResult('cookieResult', `❌ Cookie检查失败\n错误: ${error.message}`, 'error');
                }
            } else {
                showResult('cookieResult', '⚠️ 此页面无法访问Chrome扩展API\n请在插件弹窗中测试Cookie功能', 'error');
            }
        }

        // 清除测试Cookie
        async function clearCookies() {
            showResult('cookieResult', '清除测试Cookie功能仅在插件环境中可用', 'info');
        }

        // 完整API测试
        async function testFullAPI() {
            showResult('fullTestResult', '开始完整流程测试...', 'info');
            
            const serverUrl = getServerUrl();
            let results = [];
            
            // 1. 基础连接测试
            try {
                const response = await fetch(serverUrl + '/', {mode: 'cors', credentials: 'omit'});
                results.push(`✅ 基础连接: ${response.status}`);
            } catch (error) {
                results.push(`❌ 基础连接失败: ${error.message}`);
                showResult('fullTestResult', results.join('\n'), 'error');
                return;
            }
            
            // 2. CORS测试
            try {
                const response = await fetch(serverUrl + '/api/v1/reqAgent/tapd/parse', {
                    method: 'OPTIONS',
                    mode: 'cors',
                    headers: {'Origin': 'chrome-extension://test'}
                });
                results.push(`✅ CORS预检: ${response.status}`);
            } catch (error) {
                results.push(`❌ CORS预检失败: ${error.message}`);
            }
            
            // 3. API测试
            try {
                const testData = {
                    url: "https://www.tapd.cn/test",
                    title: "测试",
                    content: "测试内容",
                    user_id: 1,
                    project_id: 2
                };
                
                const response = await fetch(serverUrl + '/api/v1/reqAgent/tapd/parse', {
                    method: 'POST',
                    mode: 'cors',
                    credentials: 'omit',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    results.push(`✅ API测试: 成功 (${result.status || 'unknown'})`);
                } else {
                    results.push(`⚠️ API测试: HTTP ${response.status}`);
                }
            } catch (error) {
                results.push(`❌ API测试失败: ${error.message}`);
            }
            
            const allSuccess = results.every(r => r.startsWith('✅'));
            showResult('fullTestResult', results.join('\n'), allSuccess ? 'success' : 'error');
        }
    </script>
</body>
</html>

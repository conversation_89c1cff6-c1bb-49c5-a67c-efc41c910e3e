#!/bin/bash

# Docker部署检查脚本
echo "🐳 Docker部署状态检查"
echo "===================="

# 检查Docker容器状态
echo "📦 检查容器状态:"
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "🔍 检查端口映射:"
docker port $(docker ps -q) 2>/dev/null || echo "没有运行的容器"

echo ""
echo "📊 检查容器资源使用:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo ""
echo "📝 检查最近的容器日志:"
CONTAINER_ID=$(docker ps -q | head -1)
if [ ! -z "$CONTAINER_ID" ]; then
    echo "容器ID: $CONTAINER_ID"
    echo "最近10行日志:"
    docker logs --tail 10 $CONTAINER_ID
else
    echo "没有运行的容器"
fi

echo ""
echo "🌐 检查网络配置:"
docker network ls

echo ""
echo "🔧 检查Docker Compose服务:"
if [ -f "docker-compose.yml" ] || [ -f "docker-compose-with-postgres.yml" ]; then
    docker-compose ps 2>/dev/null || docker compose ps 2>/dev/null || echo "Docker Compose未运行"
else
    echo "未找到docker-compose文件"
fi

echo ""
echo "🚀 建议的测试命令:"
echo "1. 测试本地访问: curl http://localhost:9999/"
echo "2. 测试API: curl http://localhost:9999/api/v1/reqAgent/tapd/list"
echo "3. 测试远程访问: python test_remote_access.py <服务器IP>"

echo ""
echo "📋 插件配置参考:"
echo "应用接收URL: http://<服务器IP>:9999/api/v1/reqAgent/tapd/parse"
echo "认证令牌: dev"

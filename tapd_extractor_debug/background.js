// 调试版本 - 简化的background脚本
console.log('TAPD Debug Extension: Background script loaded');

// 监听消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  console.log('Background: 收到消息', request.action);

  // 处理代理请求
  if (request.action === 'proxyRequest') {
    console.log('Background: 开始处理代理请求');
    console.log('Background: 目标URL:', request.targetUrl);
    console.log('Background: 数据大小:', JSON.stringify(request.data).length, '字符');

    // 简单的网络测试
    testNetworkConnectivity(request.targetUrl)
      .then(result => {
        console.log('Background: 网络测试结果:', result);
        if (result.success) {
          return sendActualRequest(request.targetUrl, request.data);
        } else {
          throw new Error(`网络测试失败: ${result.error}`);
        }
      })
      .then(data => {
        console.log('Background: 请求成功');
        sendResponse({success: true, data: data});
      })
      .catch(error => {
        console.error('Background: 请求失败:', error);
        sendResponse({success: false, error: error.message});
      });

    return true; // 保持异步响应通道
  }
});

// 网络连通性测试
async function testNetworkConnectivity(targetUrl) {
  console.log('Background: 开始网络连通性测试');
  
  try {
    const url = new URL(targetUrl);
    const baseUrl = `${url.protocol}//${url.host}`;
    
    console.log('Background: 测试基础URL:', baseUrl);
    
    // 1. 测试基础连接
    try {
      const response = await fetch(baseUrl, {
        method: 'HEAD',
        mode: 'no-cors', // 绕过CORS检查
        cache: 'no-cache'
      });
      console.log('Background: 基础连接测试 - no-cors模式成功');
    } catch (error) {
      console.warn('Background: 基础连接测试 - no-cors模式失败:', error.message);
    }

    // 2. 测试CORS连接
    try {
      const response = await fetch(baseUrl, {
        method: 'HEAD',
        mode: 'cors',
        cache: 'no-cache'
      });
      console.log('Background: CORS连接测试成功, 状态:', response.status);
      return {success: true, message: 'CORS连接正常'};
    } catch (error) {
      console.warn('Background: CORS连接测试失败:', error.message);
      return {success: false, error: `CORS连接失败: ${error.message}`};
    }

  } catch (error) {
    console.error('Background: 网络测试异常:', error);
    return {success: false, error: `网络测试异常: ${error.message}`};
  }
}

// 发送实际请求
async function sendActualRequest(targetUrl, data) {
  console.log('Background: 发送实际请求到:', targetUrl);
  
  const requestOptions = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
    mode: 'cors',
    credentials: 'omit',
    cache: 'no-cache'
  };

  console.log('Background: 请求配置:', JSON.stringify(requestOptions, null, 2));

  try {
    const response = await fetch(targetUrl, requestOptions);
    
    console.log('Background: 收到响应');
    console.log('Background: 状态码:', response.status);
    console.log('Background: 状态文本:', response.statusText);
    console.log('Background: 响应头:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Background: 响应错误内容:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Background: 成功解析JSON响应');
    return result;

  } catch (error) {
    console.error('Background: 请求异常:', error);
    console.error('Background: 错误类型:', error.constructor.name);
    console.error('Background: 错误消息:', error.message);
    console.error('Background: 错误堆栈:', error.stack);
    throw error;
  }
}

// 简化的图片获取（暂时返回占位符）
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'fetchImage') {
    console.log('Background: 图片请求 - 返回占位符');
    // 暂时返回占位图片，避免图片获取干扰主要功能测试
    sendResponse({
      success: true, 
      data: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII='
    });
    return true;
  }
});

// 标签页更新监听
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('tapd.cn')) {
    chrome.action.setBadgeText({text: 'DEBUG', tabId: tabId});
    chrome.action.setBadgeBackgroundColor({color: '#ff6600', tabId: tabId});
  }
});

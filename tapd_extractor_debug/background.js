// 调试版本 - 简化的background脚本
console.log('TAPD Debug Extension: Background script loaded');

// 监听消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  console.log('Background: 收到消息', request.action);

  // 处理代理请求
  if (request.action === 'proxyRequest') {
    console.log('Background: 开始处理代理请求');
    console.log('Background: 目标URL:', request.targetUrl);
    console.log('Background: 数据大小:', JSON.stringify(request.data).length, '字符');

    // 简单的网络测试
    testNetworkConnectivity(request.targetUrl)
      .then(result => {
        console.log('Background: 网络测试结果:', result);
        if (result.success) {
          return sendActualRequest(request.targetUrl, request.data);
        } else {
          throw new Error(`网络测试失败: ${result.error}`);
        }
      })
      .then(data => {
        console.log('Background: 请求成功');
        sendResponse({success: true, data: data});
      })
      .catch(error => {
        console.error('Background: 请求失败:', error);
        sendResponse({success: false, error: error.message});
      });

    return true; // 保持异步响应通道
  }
});

// 网络连通性测试
async function testNetworkConnectivity(targetUrl) {
  console.log('Background: 开始网络连通性测试');
  
  try {
    const url = new URL(targetUrl);
    const baseUrl = `${url.protocol}//${url.host}`;
    
    console.log('Background: 测试基础URL:', baseUrl);
    
    // 1. 测试基础连接
    try {
      const response = await fetch(baseUrl, {
        method: 'HEAD',
        mode: 'no-cors', // 绕过CORS检查
        cache: 'no-cache'
      });
      console.log('Background: 基础连接测试 - no-cors模式成功');
    } catch (error) {
      console.warn('Background: 基础连接测试 - no-cors模式失败:', error.message);
    }

    // 2. 测试CORS连接
    try {
      const response = await fetch(baseUrl, {
        method: 'HEAD',
        mode: 'cors',
        cache: 'no-cache'
      });
      console.log('Background: CORS连接测试成功, 状态:', response.status);
      return {success: true, message: 'CORS连接正常'};
    } catch (error) {
      console.warn('Background: CORS连接测试失败:', error.message);
      return {success: false, error: `CORS连接失败: ${error.message}`};
    }

  } catch (error) {
    console.error('Background: 网络测试异常:', error);
    return {success: false, error: `网络测试异常: ${error.message}`};
  }
}

// 发送实际请求
async function sendActualRequest(targetUrl, data) {
  console.log('Background: 发送实际请求到:', targetUrl);
  
  const requestOptions = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
    mode: 'cors',
    credentials: 'omit',
    cache: 'no-cache'
  };

  console.log('Background: 请求配置:', JSON.stringify(requestOptions, null, 2));

  try {
    const response = await fetch(targetUrl, requestOptions);
    
    console.log('Background: 收到响应');
    console.log('Background: 状态码:', response.status);
    console.log('Background: 状态文本:', response.statusText);
    console.log('Background: 响应头:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Background: 响应错误内容:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Background: 成功解析JSON响应');
    return result;

  } catch (error) {
    console.error('Background: 请求异常:', error);
    console.error('Background: 错误类型:', error.constructor.name);
    console.error('Background: 错误消息:', error.message);
    console.error('Background: 错误堆栈:', error.stack);
    throw error;
  }
}

// 完整的图片获取功能
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'fetchImage') {
    console.log('Background: 开始获取图片:', request.url);
    fetchImageWithCookies(request.url)
      .then(base64 => {
        console.log('Background: 图片获取成功，大小:', base64.length);
        sendResponse({success: true, data: base64});
      })
      .catch(error => {
        console.error('Background: 图片获取失败:', error);
        // 失败时返回占位图片
        sendResponse({
          success: true,
          data: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII='
        });
      });
    return true;
  }
});

// 使用Cookie获取图片并转换为Base64
async function fetchImageWithCookies(url) {
  console.log('Background: 开始获取图片', url);

  try {
    // 处理TAPD特殊图片URL
    let processedUrl = url;

    // 处理compress图片URL，将其转换为原始图片URL
    if (url.includes('compress/compress_img') && url.includes('?src=')) {
      const srcMatch = url.match(/\?src=([^&]+)/);
      if (srcMatch && srcMatch[1]) {
        processedUrl = decodeURIComponent(srcMatch[1]);
        console.log(`Background: 处理compress图片URL，原始URL: ${processedUrl}`);
      }
    }

    // 获取所有TAPD相关的Cookie
    const cookies = await new Promise(resolve => {
      chrome.cookies.getAll({domain: 'tapd.cn'}, resolve);
    });

    // 构建Cookie字符串
    const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    console.log('Background: 获取到Cookie数量', cookies.length);

    // 记录Cookie信息但继续处理
    const hasTapdSession = cookieString.includes('tapdsession');
    const hasTU = cookieString.includes('t_u');
    console.log(`Background: Cookie检查 - tapdsession: ${hasTapdSession}, t_u: ${hasTU}`);

    // 发送请求
    const response = await fetch(processedUrl, {
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        'Referer': 'https://www.tapd.cn/',
        'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Origin': 'https://www.tapd.cn',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'Sec-Fetch-Dest': 'image',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site'
      },
      credentials: 'include',
      redirect: 'follow'
    });

    if (!response.ok) {
      console.warn(`Background: HTTP响应不成功: ${response.status}`);
      // 尝试使用占位图片替代
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII=';
    }

    const blob = await response.blob();

    // 检查blob类型
    if (!blob.type.startsWith('image/')) {
      console.warn(`Background: 获取的内容不是图片，而是 ${blob.type}，大小: ${blob.size} 字节`);
      // 尝试使用占位图片替代
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII=';
    }

    console.log(`Background: 成功获取图片，类型: ${blob.type}，大小: ${blob.size} 字节`);

    // 转换为Base64
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Background: 获取图片失败', error);
    // 出错时返回占位图片
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVR4nO3bMQ0AMAzAsIF/z5AvHx2kgr7dM7M7PnC+A/gzJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBiGxDAkhiExDIlhSAxDYhgSw5AYhsQwJIYhMQyJYUgMQ2IYEsOQGIbEMCSGITEMiWFIDENiGBLDkBgPAOvvAX1QsBEAAAAASUVORK5CYII=';
  }
}

// 标签页更新监听
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('tapd.cn')) {
    chrome.action.setBadgeText({text: 'DEBUG', tabId: tabId});
    chrome.action.setBadgeBackgroundColor({color: '#ff6600', tabId: tabId});
  }
});

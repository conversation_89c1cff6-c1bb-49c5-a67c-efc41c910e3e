// 调试版本的popup脚本
let debugLogs = [];

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('Debug Popup: 页面加载完成');
  
  // 加载保存的设置
  chrome.storage.local.get(['serverUrl', 'includeImages'], function(result) {
    if (result.serverUrl) {
      document.getElementById('serverUrl').value = result.serverUrl;
    }
    // 加载图片选项设置，默认为true
    const includeImages = result.includeImages !== undefined ? result.includeImages : true;
    document.getElementById('includeImages').checked = includeImages;
    updateApiUrl();
  });

  // 监听URL输入变化
  document.getElementById('serverUrl').addEventListener('input', function() {
    updateApiUrl();
    saveSettings();
  });

  // 监听图片选项变化
  document.getElementById('includeImages').addEventListener('change', function() {
    saveSettings();
    const isChecked = this.checked;
    addDebugLog(`图片发送选项已${isChecked ? '启用' : '禁用'}`);
  });

  // 绑定按钮事件
  document.getElementById('testConnectionBtn').addEventListener('click', testConnection);
  document.getElementById('testAPIBtn').addEventListener('click', testAPI);
  document.getElementById('extractContentBtn').addEventListener('click', extractContent);
  document.getElementById('clearDebugBtn').addEventListener('click', clearDebug);
  document.getElementById('exportDebugBtn').addEventListener('click', exportDebug);

  addDebugLog('插件初始化完成');
});

// 更新API URL显示
function updateApiUrl() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  const apiUrl = serverUrl ? `${serverUrl}/api/v1/reqAgent/tapd/parse` : '';
  document.getElementById('apiUrl').value = apiUrl;
}

// 保存所有设置
function saveSettings() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  const includeImages = document.getElementById('includeImages').checked;
  chrome.storage.local.set({
    serverUrl: serverUrl,
    includeImages: includeImages
  });
}

// 显示状态
function showStatus(elementId, message, type = 'info') {
  const element = document.getElementById(elementId);
  element.textContent = message;
  element.className = `status ${type}`;
  element.style.display = 'block';
  
  addDebugLog(`[${type.toUpperCase()}] ${message}`);
}

// 添加调试日志
function addDebugLog(message) {
  const timestamp = new Date().toLocaleTimeString();
  const logEntry = `[${timestamp}] ${message}`;
  debugLogs.push(logEntry);
  
  // 只保留最近50条日志
  if (debugLogs.length > 50) {
    debugLogs.shift();
  }
  
  updateDebugDisplay();
}

// 更新调试信息显示
function updateDebugDisplay() {
  const debugElement = document.getElementById('debugInfo');
  debugElement.textContent = debugLogs.slice(-10).join('\n');
  debugElement.scrollTop = debugElement.scrollHeight;
}

// 测试连接
async function testConnection() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  
  if (!serverUrl) {
    showStatus('testStatus', '请输入服务器URL', 'error');
    return;
  }

  showStatus('testStatus', '正在测试连接...', 'info');
  addDebugLog(`开始测试连接: ${serverUrl}`);

  try {
    // 测试基础连接
    addDebugLog('尝试基础连接测试...');
    const response = await fetch(serverUrl, {
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-cache'
    });
    
    addDebugLog('基础连接测试完成');
    
    // 测试CORS
    addDebugLog('尝试CORS连接测试...');
    try {
      const corsResponse = await fetch(serverUrl, {
        method: 'HEAD',
        mode: 'cors',
        cache: 'no-cache'
      });
      
      addDebugLog(`CORS测试成功: ${corsResponse.status}`);
      showStatus('testStatus', `✅ 连接成功 (状态: ${corsResponse.status})`, 'success');
      
    } catch (corsError) {
      addDebugLog(`CORS测试失败: ${corsError.message}`);
      showStatus('testStatus', `⚠️ 基础连接成功，但CORS可能有问题: ${corsError.message}`, 'error');
    }
    
  } catch (error) {
    addDebugLog(`连接测试失败: ${error.message}`);
    showStatus('testStatus', `❌ 连接失败: ${error.message}`, 'error');
  }
}

// 测试API - 增强版
async function testAPI() {
  const serverUrl = document.getElementById('serverUrl').value.trim();

  if (!serverUrl) {
    showStatus('testStatus', '请输入服务器URL', 'error');
    return;
  }

  showStatus('testStatus', '正在进行多重API测试...', 'info');
  addDebugLog(`开始多重API测试，服务器: ${serverUrl}`);

  const testData = {
    url: "https://www.tapd.cn/test/story/detail/123456",
    title: "调试测试需求",
    content: "<div>这是一个调试测试</div>",
    cookies: "debug_cookie=test_value",
    user_id: 1,
    project_id: 2
  };

  // 测试多个可能的API端点
  const testEndpoints = [
    `${serverUrl}/api/v1/reqAgent/tapd/parse`,  // 标准路径
    `${serverUrl}:9999/api/v1/reqAgent/tapd/parse`,  // 直接端口
    `${serverUrl}/api/v1/reqAgent/tapd/list`,  // 列表API测试
    `${serverUrl}/api/v1/public/tapd-parse`,  // 公开API
  ];

  let successCount = 0;
  let results = [];

  for (const [index, apiUrl] of testEndpoints.entries()) {
    try {
      addDebugLog(`测试端点 ${index + 1}: ${apiUrl}`);

      const isListAPI = apiUrl.includes('/list');
      const method = isListAPI ? 'GET' : 'POST';
      const requestOptions = {
        method: method,
        mode: 'cors',
        credentials: 'omit',
        headers: isListAPI ? {} : { 'Content-Type': 'application/json' }
      };

      if (!isListAPI) {
        requestOptions.body = JSON.stringify(testData);
      }

      const response = await fetch(apiUrl, requestOptions);

      addDebugLog(`端点 ${index + 1} 响应状态: ${response.status}`);

      if (response.ok) {
        const result = await response.json();
        addDebugLog(`端点 ${index + 1} 成功: ${JSON.stringify(result).substring(0, 100)}...`);
        results.push(`✅ ${apiUrl}: 成功 (${response.status})`);
        successCount++;
      } else {
        const errorText = await response.text();
        addDebugLog(`端点 ${index + 1} 失败: ${errorText.substring(0, 100)}...`);
        results.push(`❌ ${apiUrl}: HTTP ${response.status}`);
      }

    } catch (error) {
      addDebugLog(`端点 ${index + 1} 异常: ${error.message}`);
      results.push(`❌ ${apiUrl}: ${error.message}`);
    }

    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // 显示综合结果
  const summary = `API测试完成: ${successCount}/${testEndpoints.length} 成功\n\n${results.join('\n')}`;

  if (successCount > 0) {
    showStatus('testStatus', summary, 'success');
  } else {
    showStatus('testStatus', summary, 'error');
  }
}

// 提取内容
function extractContent() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  
  if (!serverUrl) {
    showStatus('extractStatus', '请先设置服务器URL', 'error');
    return;
  }

  showStatus('extractStatus', '正在提取内容...', 'info');
  addDebugLog('开始提取TAPD内容');

  // 获取当前标签页
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentTab = tabs[0];
    
    if (!currentTab.url.includes('tapd.cn')) {
      showStatus('extractStatus', '请在TAPD页面使用此功能', 'error');
      return;
    }

    addDebugLog(`当前页面: ${currentTab.url}`);

    // 获取图片选项设置
    const includeImages = document.getElementById('includeImages').checked;
    addDebugLog(`图片处理选项: ${includeImages ? '启用' : '禁用'}`);

    // 注入内容脚本
    chrome.scripting.executeScript({
      target: {tabId: currentTab.id},
      files: ['content.js']
    }, function() {
      // 发送提取消息，包含图片选项
      chrome.tabs.sendMessage(currentTab.id, {
        action: 'extractContent',
        includeImages: includeImages
      }, function(response) {
        if (chrome.runtime.lastError) {
          const error = chrome.runtime.lastError.message;
          addDebugLog(`内容脚本错误: ${error}`);
          showStatus('extractStatus', `内容提取失败: ${error}`, 'error');
          return;
        }

        if (!response || !response.success) {
          const error = response ? response.error : '无响应';
          addDebugLog(`内容提取失败: ${error}`);
          showStatus('extractStatus', `内容提取失败: ${error}`, 'error');
          return;
        }

        addDebugLog('内容提取成功，准备发送到服务器');

        // 记录图片处理状态
        const imagesProcessed = response.imagesProcessed;
        addDebugLog(`图片处理状态: ${imagesProcessed ? '已处理' : '已跳过'}`);

        // 准备数据
        const data = {
          url: currentTab.url,
          title: response.title,
          content: response.content,
          cookies: 'debug_session=test',
          user_id: 1,
          project_id: 2
        };

        addDebugLog(`准备发送数据，内容长度: ${data.content.length}`);

        // 发送到服务器
        chrome.runtime.sendMessage({
          action: 'proxyRequest',
          targetUrl: `${serverUrl}/api/v1/reqAgent/tapd/parse`,
          data: data
        }, function(result) {
          if (result && result.success) {
            addDebugLog('服务器处理成功');
            showStatus('extractStatus', '✅ 内容提取并发送成功', 'success');
          } else {
            const error = result ? result.error : '未知错误';
            addDebugLog(`服务器处理失败: ${error}`);
            showStatus('extractStatus', `❌ 发送失败: ${error}`, 'error');
          }
        });
      });
    });
  });
}

// 清除调试日志
function clearDebug() {
  debugLogs = [];
  updateDebugDisplay();
  addDebugLog('调试日志已清除');
}

// 导出调试日志
function exportDebug() {
  const logText = debugLogs.join('\n');
  const blob = new Blob([logText], {type: 'text/plain'});
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = `tapd-debug-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
  a.click();
  
  URL.revokeObjectURL(url);
  addDebugLog('调试日志已导出');
}

// 调试版本的popup脚本
let debugLogs = [];

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('Debug Popup: 页面加载完成');
  
  // 加载保存的URL
  chrome.storage.local.get(['serverUrl'], function(result) {
    if (result.serverUrl) {
      document.getElementById('serverUrl').value = result.serverUrl;
    }
    updateApiUrl();
  });

  // 监听URL输入变化
  document.getElementById('serverUrl').addEventListener('input', function() {
    updateApiUrl();
    saveServerUrl();
  });

  addDebugLog('插件初始化完成');
});

// 更新API URL显示
function updateApiUrl() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  const apiUrl = serverUrl ? `${serverUrl}/api/v1/reqAgent/tapd/parse` : '';
  document.getElementById('apiUrl').value = apiUrl;
}

// 保存服务器URL
function saveServerUrl() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  chrome.storage.local.set({serverUrl: serverUrl});
}

// 显示状态
function showStatus(elementId, message, type = 'info') {
  const element = document.getElementById(elementId);
  element.textContent = message;
  element.className = `status ${type}`;
  element.style.display = 'block';
  
  addDebugLog(`[${type.toUpperCase()}] ${message}`);
}

// 添加调试日志
function addDebugLog(message) {
  const timestamp = new Date().toLocaleTimeString();
  const logEntry = `[${timestamp}] ${message}`;
  debugLogs.push(logEntry);
  
  // 只保留最近50条日志
  if (debugLogs.length > 50) {
    debugLogs.shift();
  }
  
  updateDebugDisplay();
}

// 更新调试信息显示
function updateDebugDisplay() {
  const debugElement = document.getElementById('debugInfo');
  debugElement.textContent = debugLogs.slice(-10).join('\n');
  debugElement.scrollTop = debugElement.scrollHeight;
}

// 测试连接
async function testConnection() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  
  if (!serverUrl) {
    showStatus('testStatus', '请输入服务器URL', 'error');
    return;
  }

  showStatus('testStatus', '正在测试连接...', 'info');
  addDebugLog(`开始测试连接: ${serverUrl}`);

  try {
    // 测试基础连接
    addDebugLog('尝试基础连接测试...');
    const response = await fetch(serverUrl, {
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-cache'
    });
    
    addDebugLog('基础连接测试完成');
    
    // 测试CORS
    addDebugLog('尝试CORS连接测试...');
    try {
      const corsResponse = await fetch(serverUrl, {
        method: 'HEAD',
        mode: 'cors',
        cache: 'no-cache'
      });
      
      addDebugLog(`CORS测试成功: ${corsResponse.status}`);
      showStatus('testStatus', `✅ 连接成功 (状态: ${corsResponse.status})`, 'success');
      
    } catch (corsError) {
      addDebugLog(`CORS测试失败: ${corsError.message}`);
      showStatus('testStatus', `⚠️ 基础连接成功，但CORS可能有问题: ${corsError.message}`, 'error');
    }
    
  } catch (error) {
    addDebugLog(`连接测试失败: ${error.message}`);
    showStatus('testStatus', `❌ 连接失败: ${error.message}`, 'error');
  }
}

// 测试API
async function testAPI() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  
  if (!serverUrl) {
    showStatus('testStatus', '请输入服务器URL', 'error');
    return;
  }

  const apiUrl = `${serverUrl}/api/v1/reqAgent/tapd/parse`;
  showStatus('testStatus', '正在测试API...', 'info');
  addDebugLog(`开始API测试: ${apiUrl}`);

  const testData = {
    url: "https://www.tapd.cn/test/story/detail/123456",
    title: "调试测试需求",
    content: "<div>这是一个调试测试</div>",
    cookies: "debug_cookie=test_value",
    user_id: 1,
    project_id: 2
  };

  try {
    addDebugLog('发送API测试请求...');
    addDebugLog(`请求数据: ${JSON.stringify(testData)}`);
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      mode: 'cors',
      credentials: 'omit',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    addDebugLog(`API响应状态: ${response.status}`);
    addDebugLog(`API响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);

    if (response.ok) {
      const result = await response.json();
      addDebugLog(`API响应数据: ${JSON.stringify(result)}`);
      showStatus('testStatus', `✅ API测试成功: ${result.status || 'success'}`, 'success');
    } else {
      const errorText = await response.text();
      addDebugLog(`API错误响应: ${errorText}`);
      showStatus('testStatus', `❌ API测试失败: HTTP ${response.status}`, 'error');
    }

  } catch (error) {
    addDebugLog(`API测试异常: ${error.message}`);
    showStatus('testStatus', `❌ API测试失败: ${error.message}`, 'error');
  }
}

// 提取内容
function extractContent() {
  const serverUrl = document.getElementById('serverUrl').value.trim();
  
  if (!serverUrl) {
    showStatus('extractStatus', '请先设置服务器URL', 'error');
    return;
  }

  showStatus('extractStatus', '正在提取内容...', 'info');
  addDebugLog('开始提取TAPD内容');

  // 获取当前标签页
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentTab = tabs[0];
    
    if (!currentTab.url.includes('tapd.cn')) {
      showStatus('extractStatus', '请在TAPD页面使用此功能', 'error');
      return;
    }

    addDebugLog(`当前页面: ${currentTab.url}`);

    // 注入内容脚本
    chrome.scripting.executeScript({
      target: {tabId: currentTab.id},
      files: ['content.js']
    }, function() {
      // 发送提取消息
      chrome.tabs.sendMessage(currentTab.id, {action: 'extractContent'}, function(response) {
        if (chrome.runtime.lastError) {
          const error = chrome.runtime.lastError.message;
          addDebugLog(`内容脚本错误: ${error}`);
          showStatus('extractStatus', `内容提取失败: ${error}`, 'error');
          return;
        }

        if (!response || !response.success) {
          const error = response ? response.error : '无响应';
          addDebugLog(`内容提取失败: ${error}`);
          showStatus('extractStatus', `内容提取失败: ${error}`, 'error');
          return;
        }

        addDebugLog('内容提取成功，准备发送到服务器');
        
        // 准备数据
        const data = {
          url: currentTab.url,
          title: response.title,
          content: response.content,
          cookies: 'debug_session=test',
          user_id: 1,
          project_id: 2
        };

        addDebugLog(`准备发送数据，内容长度: ${data.content.length}`);

        // 发送到服务器
        chrome.runtime.sendMessage({
          action: 'proxyRequest',
          targetUrl: `${serverUrl}/api/v1/reqAgent/tapd/parse`,
          data: data
        }, function(result) {
          if (result && result.success) {
            addDebugLog('服务器处理成功');
            showStatus('extractStatus', '✅ 内容提取并发送成功', 'success');
          } else {
            const error = result ? result.error : '未知错误';
            addDebugLog(`服务器处理失败: ${error}`);
            showStatus('extractStatus', `❌ 发送失败: ${error}`, 'error');
          }
        });
      });
    });
  });
}

// 清除调试日志
function clearDebug() {
  debugLogs = [];
  updateDebugDisplay();
  addDebugLog('调试日志已清除');
}

// 导出调试日志
function exportDebug() {
  const logText = debugLogs.join('\n');
  const blob = new Blob([logText], {type: 'text/plain'});
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = `tapd-debug-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
  a.click();
  
  URL.revokeObjectURL(url);
  addDebugLog('调试日志已导出');
}

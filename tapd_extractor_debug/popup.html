<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>TAPD Content Extractor (Debug)</title>
  <style>
    body {
      width: 400px;
      padding: 20px;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      color: #fff;
    }
    
    .debug-badge {
      background: #ff6600;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      margin-left: 10px;
    }
    
    .section {
      background: rgba(255, 255, 255, 0.1);
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      backdrop-filter: blur(10px);
    }
    
    .section h3 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #fff;
    }
    
    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 4px;
      margin: 5px 0;
      box-sizing: border-box;
    }
    
    button {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px 5px 5px 0;
      font-size: 12px;
    }
    
    button:hover {
      background: #45a049;
    }
    
    button.test {
      background: #2196F3;
    }
    
    button.test:hover {
      background: #1976D2;
    }
    
    .status {
      margin: 10px 0;
      padding: 8px;
      border-radius: 4px;
      font-size: 12px;
      word-wrap: break-word;
    }
    
    .status.success {
      background: rgba(76, 175, 80, 0.3);
      border: 1px solid #4CAF50;
    }
    
    .status.error {
      background: rgba(244, 67, 54, 0.3);
      border: 1px solid #f44336;
    }
    
    .status.info {
      background: rgba(33, 150, 243, 0.3);
      border: 1px solid #2196F3;
    }
    
    .debug-info {
      font-family: monospace;
      font-size: 10px;
      max-height: 100px;
      overflow-y: auto;
      background: rgba(0, 0, 0, 0.3);
      padding: 8px;
      border-radius: 4px;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>TAPD 提取器 <span class="debug-badge">DEBUG</span></h1>
  </div>

  <div class="section">
    <h3>🔧 网络测试</h3>
    <input type="text" id="serverUrl" placeholder="http://172.16.1.241:9999" value="http://172.16.1.241:9999">
    <button class="test" onclick="testConnection()">测试连接</button>
    <button class="test" onclick="testAPI()">测试API</button>
    <div id="testStatus" class="status" style="display: none;"></div>
  </div>

  <div class="section">
    <h3>📝 TAPD提取</h3>
    <input type="text" id="apiUrl" placeholder="API地址" readonly>
    <button onclick="extractContent()">提取内容</button>
    <div id="extractStatus" class="status" style="display: none;"></div>
  </div>

  <div class="section">
    <h3>🐛 调试信息</h3>
    <button class="test" onclick="clearDebug()">清除日志</button>
    <button class="test" onclick="exportDebug()">导出日志</button>
    <div id="debugInfo" class="debug-info"></div>
  </div>

  <script src="popup.js"></script>
</body>
</html>

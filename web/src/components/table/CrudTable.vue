<template>
  <div v-bind="$attrs" class="crud-table-container">
    <QueryBar v-if="$slots.queryBar" class="crud-table-query-bar" mb-30 @search="handleSearch" @reset="handleReset">
      <slot name="queryBar" />
    </QueryBar>

    <n-data-table
      :remote="remote"
      :loading="loading"
      :columns="columns"
      :data="tableData"
      :scroll-x="scrollX"
      :scroll-y="scrollY"
      :row-key="(row) => row[rowKey]"
      :pagination="isPagination ? pagination : false"
      @update:checked-row-keys="onChecked"
      @update:page="onPageChange"
      @update:page-size="onPageSizeChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import axios from 'axios'
const props = defineProps({
  /**
   * @remote true: 后端分页  false： 前端分页
   */
  remote: {
    type: Boolean,
    default: true,
  },
  /**
   * @remote 是否分页
   */
  isPagination: {
    type: Boolean,
    default: true,
  },
  scrollX: {
    type: Number,
    default: 450,
  },
  scrollY: {
    type: Number,
    default: 400, // 设置一个合适的默认高度
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  columns: {
    type: Array,
    required: true,
  },
  /** queryBar中的参数 */
  queryItems: {
    type: Object,
    default() {
      return {}
    },
  },
  /** 补充参数（可选） */
  extraParams: {
    type: Object,
    default() {
      return {}
    },
  },
  /**
   * ! 约定接口入参出参
   * * 分页模式需约定分页接口入参
   *    @page_size 分页参数：一页展示多少条，默认10
   *    @page   分页参数：页码，默认1
   */
  getData: {
    type: Function,
    required: true,
  },
  /** 分页配置 */
  pagination: {
    type: Object,
    default() {
      return {
        page: 1,
        pageSize: 10,
        pageCount: 0,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100],
        itemCount: 0,
      }
    },
  },
})

const emit = defineEmits(['update:queryItems', 'update:pagination', 'onChecked', 'onDataChange'])
const loading = ref(false)
const initQuery = { ...props.queryItems }
const tableData = ref([])

// 添加监听，确保表格数据变化时重新渲染
watch(tableData, (newData) => {
  console.log('表格数据变化:', newData);
}, { deep: true })
// 使用 props 中的 pagination
const pagination = reactive({
  ...props.pagination,
  // 确保必要的属性存在
  page: props.pagination?.page || 1,
  pageSize: props.pagination?.pageSize || 10,
  pageSizes: props.pagination?.pageSizes || [10, 20, 50, 100],
  showSizePicker: props.pagination?.showSizePicker !== false,
  prefix({ itemCount }) {
    return `共 ${itemCount} 条`
  }
})

async function handleQuery() {
  try {
    console.log('handleQuery 被调用');
    loading.value = true
    let paginationParams = {}
    // 如果非分页模式或者使用前端分页,则无需传分页参数
    if (props.isPagination && props.remote) {
      paginationParams = {
        page: pagination.page,
        page_size: pagination.pageSize || 10
      }

      // 打印分页参数，便于调试
      console.log('分页参数:', paginationParams, '类型:', typeof paginationParams.page);
    }

    // 打印请求参数，便于调试
    console.log('请求参数:', {
      queryItems: props.queryItems,
      extraParams: props.extraParams,
      paginationParams,
      isPagination: props.isPagination,
      remote: props.remote
    });

    // 特别检查状态参数
    if (props.queryItems && props.queryItems.status) {
      console.log('状态参数详情:', {
        status: props.queryItems.status,
        type: typeof props.queryItems.status,
        isArray: Array.isArray(props.queryItems.status),
        length: Array.isArray(props.queryItems.status) ? props.queryItems.status.length : 'N/A'
      });
    }

    // 确保分页参数存在
    if (props.isPagination && props.remote && (!paginationParams.page || !paginationParams.page_size)) {
      console.warn('分页参数不完整，使用默认值');
      paginationParams.page = paginationParams.page || 1;
      paginationParams.page_size = paginationParams.page_size || 10;
    }

    // 确保分页参数是数字类型
    if (paginationParams.page) {
      paginationParams.page = Number(paginationParams.page);
    }
    if (paginationParams.page_size) {
      paginationParams.page_size = Number(paginationParams.page_size);
    }

    console.log('处理后的分页参数:', paginationParams, '类型:', typeof paginationParams.page);

    const response = await props.getData({
      ...props.queryItems,
      ...props.extraParams,
      ...paginationParams,
    })

    // 打印响应数据，便于调试
    console.log('响应数据:', response);
    console.log('分页参数:', paginationParams, '响应总数:', response.total);

    // 添加更详细的日志
    console.log('响应数据类型:', typeof response);
    console.log('响应数据是否为数组:', Array.isArray(response));
    console.log('响应数据是否有data属性:', response.data !== undefined);
    if (response.data) {
      console.log('响应数据data长度:', response.data.length);
    }

    // 处理不同的响应格式
    let data = [];
    let total = 0;

    if (response.data && response.total !== undefined) {
      // 标准格式: { data, total }
      data = response.data;
      total = response.total;
    } else if (Array.isArray(response)) {
      // 数组格式: []
      data = response;
      total = response.length;
    } else if (response.data && Array.isArray(response.data)) {
      // 格式: { data: [] }
      data = response.data;
      total = response.data.length;
    } else {
      // 其他格式
      console.error('未知的响应格式:', response);
    }

    console.log('设置表格数据前:', tableData.value);
    console.log('要设置的数据:', data);

    // 确保数据是数组
    if (!Array.isArray(data)) {
      console.error('数据不是数组:', data);
      data = [];
    }

    // 使用深拷贝确保数据不会被引用影响
    tableData.value = JSON.parse(JSON.stringify(data));

    console.log('设置表格数据后:', tableData.value);
    pagination.itemCount = total || 0
    pagination.pageCount = Math.ceil(total / (pagination.pageSize || 10))

    // 确保表格数据已更新
    emit('onDataChange', tableData.value)

    // 强制更新DOM
    nextTick(() => {
      console.log('handleQuery - DOM已更新');
    });

    console.log('更新后的分页信息:', pagination);

    // 不触发分页更新事件，避免循环调用
    // 分页状态已经在内部更新，不需要通知父组件
    // emit('update:pagination', { ...pagination, _fromUserInteraction: false })

    // 打印表格数据，便于调试
    console.log('表格数据:', tableData.value);
  } catch (error) {
    console.error('获取数据错误:', error);
    tableData.value = []
    pagination.itemCount = 0
  } finally {
    emit('onDataChange', tableData.value)
    loading.value = false
  }
}
function handleSearch(params) {
  console.log('CrudTable handleSearch 被调用，参数:', params);

  // 重置分页到第一页
  pagination.page = 1

  // 清除缓存的表格数据，确保重新获取
  tableData.value = []

  // 添加延迟确保后端处理完成
  setTimeout(() => {
    // 如果传入了参数，使用传入的参数进行查询
    if (params) {
      console.log('使用传入的参数进行查询');
      handleQuery()
    } else {
      console.log('使用默认参数进行查询');
      handleQuery()
    }
  }, 100)
}
async function handleReset() {
  const queryItems = { ...props.queryItems }
  for (const key in queryItems) {
    queryItems[key] = null
  }
  emit('update:queryItems', { ...queryItems, ...initQuery })
  await nextTick()
  pagination.page = 1
  handleQuery()
}
function onPageChange(currentPage) {
  console.log('页码变更:', currentPage, '类型:', typeof currentPage)

  // 确保页码是数字类型
  currentPage = Number(currentPage);
  console.log('转换后的页码:', currentPage, '类型:', typeof currentPage)

  // 更新分页状态
  pagination.page = currentPage

  // 打印分页状态，便于调试
  console.log('更新后的分页状态:', pagination)

  // 添加标记，表示这是由用户交互触发的
  const updatedPagination = { ...pagination, _fromUserInteraction: true }
  console.log('触发分页更新事件:', updatedPagination)

  // 触发分页更新事件
  emit('update:pagination', updatedPagination)
  console.log('分页更新事件已触发')

  // 自动触发查询
  if (props.remote) {
    console.log('远程模式，自动触发查询')
    handleQuery()
  } else {
    console.log('本地模式，不触发查询')
  }
}
function onChecked(rowKeys) {
  if (props.columns.some((item) => item.type === 'selection')) {
    emit('onChecked', rowKeys)
  }
}

function onPageSizeChange(pageSize) {
  console.log('每页数量变更:', pageSize)

  // 更新分页状态
  pagination.pageSize = pageSize
  pagination.page = 1 // 切换每页数量时回到第一页

  // 打印分页状态，便于调试
  console.log('更新后的分页状态:', pagination)

  // 添加标记，表示这是由用户交互触发的
  const updatedPagination = { ...pagination, _fromUserInteraction: true }
  console.log('触发分页更新事件:', updatedPagination)
  emit('update:pagination', updatedPagination)

  // 自动触发查询
  if (props.remote) {
    handleQuery()
  }
}

defineExpose({
  handleSearch,
  handleReset,
  handleQuery,
  tableData,
  pagination, // 暴露分页对象
  // 添加直接设置表格数据的方法
  setTableData(data) {
    console.log('直接设置表格数据:', data);
    // 确保数据是数组
    if (Array.isArray(data)) {
      // 使用深拷贝确保数据不会被引用影响
      tableData.value = JSON.parse(JSON.stringify(data));
      console.log('表格数据已更新:', tableData.value);
      // 触发数据变化事件
      emit('onDataChange', tableData.value);
    } else {
      console.error('设置表格数据失败，数据不是数组:', data);
    }
  },
  // 添加分页操作方法
  updatePagination(newPagination) {
    console.log('更新分页状态:', newPagination);
    Object.assign(pagination, newPagination);
    handleQuery();
  }
})
</script>

<style lang="scss">
.crud-table-container {
  width: 100%;
}

.crud-table-query-bar {
  margin-bottom: 20px;
  width: 100%;
  display: block;
}

.n-data-table  .n-data-table-td {
  background-color: #0a2318;
}
.n-data-table  .n-data-table-th {
  background-color: #18181c;
}
</style>

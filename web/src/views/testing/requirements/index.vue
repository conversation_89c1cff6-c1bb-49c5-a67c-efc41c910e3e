<script setup>
import { h, onBeforeUnmount, onMounted, reactive, ref, resolveDirective, watch, withDirectives, nextTick } from 'vue'
import { NButton, NForm, NFormItem, NInput, NInputNumber, NPopconfirm, NTreeSelect } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
// import { loginTypeMap, loginTypeOptions } from '@/constant/data'
import api from '@/api'

defineOptions({ name: '需求管理' })

const $table = ref(null)
const queryItems = ref({
  page: 1,      // 添加默认页码
  page_size: 10, // 添加默认页大小
  tapd_url: ''  // 添加TAPD链接字段
})

// 存储共同照护项目的完整数据
const allCareProjectData = ref([])
// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
  prefix({ itemCount }) {
    return `共 ${itemCount} 条`
  }
  // 移除 onChange 和 onUpdatePageSize 回调
  // 让 CrudTable 组件自己处理分页事件
})

// 注意：我们将 scroll-y 值从 800 减小到 700，为分页区域留出更多空间
function handleSearch() {
  // 直接使用 queryItems 中的值，不再手动添加分页参数
  console.log('触发查询，查询参数:', queryItems.value);

  // 确保分页参数存在且有效
  if (!queryItems.value.page) queryItems.value.page = 1;
  if (!queryItems.value.page_size) queryItems.value.page_size = 10;

  // 对于所有查询，我们都重置请求状态，确保请求能够执行
  isRequesting.value = false;
  lastRequestTime.value = 0;
  lastRequestParams.value = null;

  // 调用表格组件的查询方法
  $table.value?.handleSearch(queryItems.value);
}

// 处理分页更新
function handlePaginationUpdate(paginationInfo) {
  console.log('收到分页更新事件:', paginationInfo);

  // 更新分页状态
  pagination.page = paginationInfo.page;
  pagination.pageSize = paginationInfo.pageSize;

  // 检查是否是共同照护项目（ID为6）
  if (queryItems.value.project_id === 6 && allCareProjectData.value.length > 0) {
    console.log('共同照护项目分页更新，使用前端分页');
    // 使用前端分页，不需要发送请求
    const page = paginationInfo.page;
    const pageSize = paginationInfo.pageSize;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;

    // 从缓存的完整数据中截取当前页的数据
    const currentPageData = allCareProjectData.value.slice(start, end);
    console.log('前端分页结果:', {
      page,
      pageSize,
      start,
      end,
      total: allCareProjectData.value.length,
      dataLength: currentPageData.length
    });

    // 直接更新表格组件的数据
    if ($table.value && $table.value.setTableData) {
      $table.value.setTableData(currentPageData);
    }

    // 更新分页信息
    pagination.itemCount = allCareProjectData.value.length;
    pagination.pageCount = Math.ceil(allCareProjectData.value.length / pageSize);

    return; // 前端分页完成，不需要发送请求
  }

  // 非共同照护项目，更新查询参数并发送请求
  queryItems.value.page = paginationInfo.page;
  queryItems.value.page_size = paginationInfo.pageSize;

  console.log('更新后的查询参数:', queryItems.value);

  // 清除请求缓存，确保分页请求能正常执行
  lastRequestParams.value = null;
  isRequesting.value = false;
  lastRequestTime.value = 0;
}
const vPermission = resolveDirective('permission')

const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleSave,
  modalForm,
  modalFormRef,
  handleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: '需求',
  initForm: { order: 0 },
  doCreate: api.createRequirement,
  doUpdate: api.updateRequirement,
  doDelete: api.deleteRequirement,
  refresh: () => {
    console.log('刷新需求列表');
    // 清除缓存的请求参数，确保重新获取数据
    lastRequestParams.value = null;
    // 重置请求标记
    isRequesting.value = false;
    // 重置请求时间
    lastRequestTime.value = 0;
    // 清空表格数据，确保重新获取
    tableData.value = [];
    // 添加延迟确保后端处理完成
    setTimeout(() => {
      if ($table.value) {
        $table.value.handleSearch();
      }
    }, 200);
  },
})

const projOption = ref([])
const isDisabled = ref(false)

// 处理项目选择变化
function handleProjectChange(value) {
  console.log('项目选择变化:', value);

  // 更新 queryItems 中的 project_id
  queryItems.value.project_id = value;

  // 重置分页到第一页
  queryItems.value.page = 1;
  queryItems.value.page_size = 10;
  pagination.page = 1;

  // 清空共同照护项目的缓存数据
  allCareProjectData.value = [];

  // 清除请求缓存
  lastRequestParams.value = null;
  isRequesting.value = false;
  lastRequestTime.value = 0;

  // 手动触发查询
  handleSearch();
}

// 添加防抖函数
const debounce = (fn, delay) => {
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

// 使用防抖处理搜索
const debouncedHandleSearch = debounce(handleSearch, 500);

// 添加组件卸载时的清理函数
let mounted = false;
// 添加滚动到顶部的函数
const scrollToTop = () => {
  console.log('执行滚动到顶部');

  try {
    // 首先强制滚动整个页面到顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  } catch (e) {
    console.error('窗口滚动异常:', e);
    // 备用方法
    window.scrollTo(0, 0);
  }

  // 滚动页面容器到顶部
  const pageContainer = document.querySelector('.page-container');
  if (pageContainer) {
    try {
      pageContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } catch (e) {
      console.error('页面容器滚动异常:', e);
      // 备用方法
      pageContainer.scrollTop = 0;
    }
  }

  // 滚动各个容器到顶部
  nextTick(() => {
    // 尝试滚动表格容器
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
      try {
        tableContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格容器滚动异常:', e);
        tableContainer.scrollTop = 0;
      }
    }

    // 尝试滚动表格内容
    const tableBody = document.querySelector('.n-data-table-base-table-body');
    if (tableBody) {
      try {
        tableBody.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格内容滚动异常:', e);
        tableBody.scrollTop = 0;
      }
    }

    // 尝试滚动所有可能的滚动容器
    const scrollContainers = [
      '.n-scrollbar-container',
      '.n-data-table-wrapper',
      '.n-data-table',
      '.crud-table-container'
    ];

    scrollContainers.forEach(selector => {
      const container = document.querySelector(selector);
      if (container) {
        try {
          container.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        } catch (e) {
          console.error(`${selector} 滚动异常:`, e);
          container.scrollTop = 0;
        }
      }
    });

    // 显示成功消息
    window.$message?.success('已滚动到顶部');
  });
};

// 添加滚动到底部的函数
const scrollToBottom = (force = false) => {
  console.log('执行滚动到底部，force =', force);

  // 首先尝试滚动整个页面容器
  if (force) {
    try {
      // 滚动整个页面
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
      });
    } catch (e) {
      console.error('窗口滚动异常:', e);
      // 备用方法
      window.scrollTo(0, document.body.scrollHeight);
    }

    // 滚动页面容器
    const pageContainer = document.querySelector('.page-container');
    if (pageContainer) {
      try {
        pageContainer.scrollTo({
          top: pageContainer.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('页面容器滚动异常:', e);
        // 备用方法
        pageContainer.scrollTop = pageContainer.scrollHeight;
      }
    }
  }

  nextTick(() => {
    // 尝试滚动表格容器
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
      try {
        tableContainer.scrollTo({
          top: tableContainer.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格容器滚动异常:', e);
        tableContainer.scrollTop = tableContainer.scrollHeight;
      }
    }

    // 尝试滚动表格内容
    const tableBody = document.querySelector('.n-data-table-base-table-body');
    if (tableBody) {
      try {
        tableBody.scrollTo({
          top: tableBody.scrollHeight,
          behavior: 'smooth'
        });
      } catch (e) {
        console.error('表格内容滚动异常:', e);
        tableBody.scrollTop = tableBody.scrollHeight;
      }
    }

    // 尝试滚动所有可能的滚动容器
    const scrollContainers = [
      '.n-scrollbar-container',
      '.n-data-table-wrapper',
      '.n-data-table',
      '.crud-table-container'
    ];

    scrollContainers.forEach(selector => {
      const container = document.querySelector(selector);
      if (container) {
        try {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          });
        } catch (e) {
          console.error(`${selector} 滚动异常:`, e);
          container.scrollTop = container.scrollHeight;
        }
      }
    });

    // 显示成功消息
    window.$message?.success('已滚动到底部');
  });
};

onMounted(async () => {
  mounted = true;
  // 确保查询参数中包含分页信息
  queryItems.value.page = 1
  queryItems.value.page_size = 10
  console.log('初始化查询参数:', queryItems.value)

  // 重置所有请求状态
  isRequesting.value = false;
  lastRequestTime.value = 0;
  lastRequestParams.value = null;

  try {
    // 先加载项目列表
    await loadProjects();

    // 直接加载需求列表，不使用延迟
    console.log('组件挂载完成，立即加载需求列表');
    if (mounted) {
      handleSearch();
    }
  } catch (error) {
    console.error('初始化加载失败:', error);
  }
})

// 添加组件卸载时的清理
onBeforeUnmount(() => {
  mounted = false;
  console.log('组件卸载，清理资源');
})

// 加载项目列表的方法
const loadProjects = async () => {
  try {
    console.log('开始加载项目列表');
    const res = await api.getProjts({ page: 1, page_size: 1000 });
    console.log('项目列表原始数据:', res);

    if (res.data && Array.isArray(res.data)) {
      projOption.value = res.data.map(item => ({
        key: item.id,
        label: item.name,
        value: Number(item.id) // 确保是数字类型
      }));
      console.log('处理后的项目列表:', projOption.value);
    } else {
      console.error('项目列表数据格式不正确:', res);
    }
  } catch (error) {
    console.error('加载项目列表失败:', error);
  }
}

// 获取需求列表的方法
const isRequesting = ref(false); // 添加请求标记
const tableData = ref([]); // 添加表格数据缓存

// 上次请求的参数，用于避免重复请求
const lastRequestParams = ref(null);
// 添加请求时间记录
const lastRequestTime = ref(0);

const getRequirementsList = async (params) => {
  console.log('获取需求列表参数:', params);

  try {
    // 设置请求标记
    isRequesting.value = true;
    lastRequestTime.value = Date.now();

    // 确保分页参数是数字类型
    const apiParams = { ...params };
    if (apiParams.page) apiParams.page = Number(apiParams.page);
    if (apiParams.page_size) apiParams.page_size = Number(apiParams.page_size);
    if (apiParams.project_id) apiParams.project_id = Number(apiParams.project_id);

    console.log('发送请求参数:', apiParams);
    const response = await api.getRequirementsList(apiParams);
    console.log('需求列表原始数据:', response);

    // 检查是否是“共同照护”项目
    if (apiParams.project_id === 6) {
      console.log('当前是“共同照护”项目，需求总数:', response.total);

      // 如果是“共同照护”项目，则在前端实现分页
      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        // 存储完整的数据列表到全局变量，以便分页时使用
        // 确保数据是深拷贝，避免引用问题
        allCareProjectData.value = JSON.parse(JSON.stringify(response.data));
        console.log('缓存共同照护项目数据，总条数:', allCareProjectData.value.length);
        console.log('缓存数据的第一项:', allCareProjectData.value[0]);

        // 如果有分页参数，则在前端实现分页
        if (params.page && params.page_size) {
          const page = Number(params.page);
          const pageSize = Number(params.page_size);
          const start = (page - 1) * pageSize;
          const end = start + pageSize;

          // 在前端实现分页
          response.data = allCareProjectData.value.slice(start, end);
          console.log('前端分页结果:', {
            page,
            pageSize,
            start,
            end,
            total: allCareProjectData.value.length,
            dataLength: response.data.length
          });
        }

        // 更新分页信息
        response.total = allCareProjectData.value.length;
        response.page = params.page ? Number(params.page) : 1;
        response.page_size = params.page_size ? Number(params.page_size) : 10;
      }
    }

    // 如果项目列表还没有加载完成，则重新加载
    if (!projOption.value || projOption.value.length === 0) {
      await loadProjects();
    }

    // 处理需求数据，确保 project_id 是数字类型
    if (response.data && Array.isArray(response.data)) {
      response.data = response.data.map(item => ({
        ...item,
        project_id: item.project_id ? Number(item.project_id) : null
      }));

      // 保存表格数据，便于重复请求时使用
      tableData.value = response.data;
    }

    // 更新分页信息
    if (response.total !== undefined) {
      pagination.itemCount = response.total;
      pagination.page = response.page || apiParams.page || 1;
      pagination.pageSize = response.page_size || apiParams.page_size || 10;
      pagination.pageCount = Math.ceil(response.total / pagination.pageSize);
    }

    // 记录本次请求参数
    lastRequestParams.value = JSON.stringify(params);
    console.log('请求成功，记录请求参数');

    return response;
  } catch (error) {
    console.error('获取需求列表失败:', error);
    return { data: [], total: 0 };
  } finally {
    // 重置请求标记
    isRequesting.value = false;
  }
}

const reqsRules = {
  name: [
    {
      required: true,
      message: '请输入需求名称',
      trigger: ['input', 'blur', 'change'],
    },
  ],
}

async function addReqs() {
  isDisabled.value = false
  handleAdd()
}

const columns = [
  {
    title: '需求名称',
    key: 'name',
    width: 'auto',
    align: 'left',
    ellipsis: { tooltip: true },
  },
  {
    title: '需求描述',
    key: 'description',
    width: 'auto',
    align: 'left',
    ellipsis: { tooltip: true },
  },

  {
    title: '备注',
    key: 'remark',
    align: 'left',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'TAPD链接',
    key: 'tapd_url',
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    render(row) {
      // 展示TAPD URL链接
      if (row.tapd_url) {
        return h(
          'a',
          {
            href: row.tapd_url,
            target: '_blank',
            style: 'color: #2080f0; text-decoration: underline;'
          },
          'TAPD需求链接'
        );
      } else {
        return h('div', {}, '');
      }
    },
  },
  // {
  //   title: '创建时间',
  //   key: 'created_at',
  //   align: 'center',
  //   width: 'auto',
  //   ellipsis: { tooltip: true },
  // },
  {
    title: '关联项目',
    key: 'project_id',
    width: 'left',
    align: 'center',
    ellipsis: { tooltip: true },
    render(row) {
      if (!projOption.value || projOption.value.length === 0) {
        return '加载中...';
      }
      // 确保 project_id 是数字类型进行比较
      const projectId = Number(row.project_id);
      console.log('需求行数据:', row, '项目ID:', projectId, '类型:', typeof projectId);

      // 查找匹配的项目
      const project = projOption.value.find(option => option.value === projectId);
      console.log('找到的项目:', project);

      // 如果找到项目则显示项目名称，否则显示未分配
      return project ? project.label : '未分配';
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 'auto',
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-left: 8px;',
              onClick: () => {
                console.log('row', row.id)
                if (row.id === 0) {
                  isDisabled.value = true
                } else {
                  isDisabled.value = false
                }
                handleEdit(row)
              },
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/requirement/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id, project_id: row.project_id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-left: 8px;',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/requirement/delete']]
              ),
            default: () => h('div', {}, '确定删除该需求吗?'),
          }
        ),
      ]
    },
  },
]
</script>

<style scoped>
/* 确保表格容器有足够的高度 */
:deep(.n-data-table) {
  max-height: 100%;
}

.table-container {
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: auto; /* 添加滚动条，确保整个表格可滚动 */
  max-height: calc(100vh - 200px); /* 设置最大高度，确保在小屏幕上也能滚动 */
}

.table-container :deep(.n-data-table-base-table-body) {
  overflow: auto !important;
}

/* 确保水平和垂直滚动条都可见 */
.table-container :deep(.n-scrollbar) {
  overflow: auto !important;
}

/* 设置滚动条样式 */
.table-container :deep(.n-scrollbar-rail) {
  z-index: 10;
}

/* 表格内容区域滚动 */
.table-container :deep(.n-data-table-base-table-body) {
  max-height: calc(100vh - 400px); /* 减小最大高度，为分页区域留出更多空间 */
  overflow: auto !important;
}

/* 确保表格内容和分页区域都可见 */
.table-container :deep(.n-data-table) {
  display: flex;
  flex-direction: column;
  height: auto !important;
  min-height: 200px;
}

/* 确保分页区域可见 */
.table-container :deep(.n-data-table-pagination) {
  margin-top: 16px;
  position: relative; /* 使用相对定位，让分页区域跟随滚动 */
  z-index: 100; /* 增大 z-index，确保分页区域始终在最上层 */
  background-color: white;
  padding: 10px 0;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影，增强视觉效果 */
  pointer-events: auto !important; /* 确保分页区域可以响应点击事件 */
}

/* 确保分页按钮可以点击 */
.table-container :deep(.n-pagination),
.table-container :deep(.n-pagination *),
.table-container :deep(.n-pagination .n-pagination-item),
.table-container :deep(.n-pagination .n-pagination-quick-jumper),
.table-container :deep(.n-pagination .n-base-selection) {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* 确保分页按钮的点击区域 */
.table-container :deep(.n-pagination-item) {
  pointer-events: auto !important;
  cursor: pointer !important;
  user-select: none;
}

.table-container :deep(.n-pagination-item:hover) {
  background-color: #f5f5f5 !important;
}

/* 调整表格底部的间距，确保最后一行可见 */
.table-container :deep(.n-data-table-wrapper) {
  padding-bottom: 70px; /* 增加底部间距，为分页区域留出更多空间 */
}

/* 确保表格容器有足够的空间 */
.table-container {
  display: flex;
  flex-direction: column;
  padding-bottom: 60px; /* 添加底部间距，为分页区域留出空间 */
}

/* 确保分页区域不被遮挡 */
.table-container::after {
  content: '';
  display: block;
  height: 60px; /* 与底部间距相同 */
  width: 100%;
  pointer-events: none; /* 不拦截点击事件 */
}

/* 固定的滚动按钮 - 底部 */
.scroll-to-bottom-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #2080f0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.scroll-to-bottom-btn:hover {
  bottom: 30px; /* 固定在底部 */
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 固定的滚动按钮 - 顶部 */
.scroll-to-top-btn {
  position: fixed;
  bottom: 100px; /* 位于底部按钮上方 */
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #18a058; /* 使用不同的颜色区分 */
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.scroll-to-top-btn:hover {
  bottom: 90px;
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}
</style>

<template>
  <!-- 业务页面 -->
  <CommonPage show-footer title="需求列表" class="overflow-auto">
    <template #action>
      <div>
        <NButton
          v-permission="'post/api/v1/requirement/create'"
          class="float-right mr-15"
          type="primary"
          @click="addReqs"
        >
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新增需求
        </NButton>
      </div>
    </template>
    <!-- 滚动到底部按钮 -->
    <div class="scroll-to-bottom-btn" @click="scrollToBottom(true)">
      <TheIcon icon="mdi:chevron-double-down" :size="20" />
    </div>

    <!-- 返回顶部按钮 -->
    <div class="scroll-to-top-btn" @click="scrollToTop()">
      <TheIcon icon="mdi:chevron-double-up" :size="20" />
    </div>
    <!-- 表格 -->
    <div class="table-container" style="min-height: 400px; max-height: calc(100vh - 150px); overflow: auto; position: relative; margin-bottom: 60px; padding-bottom: 60px;">
      <CrudTable
        ref="$table"
        v-model:query-items="queryItems"
        :columns="columns"
        :get-data="getRequirementsList"
        :pagination="pagination"
        :scroll-x="1500"
        :scroll-y="700"
        style="width: 100%; height: auto;"
        remote
        :is-pagination="true"
        @update:pagination="handlePaginationUpdate"
      >
      <template #queryBar>
        <QueryBarItem label="项目名称" :label-width="80">
          <NTreeSelect
            :value="queryItems.project_id"
            :options="projOption"
            key-field="key"
            label-field="label"
            value-field="value"
            style="width: 200px;"
            placeholder="请选择项目"
            clearable
            default-expand-all
            :disabled="isDisabled"
            @update:value="handleProjectChange"
          ></NTreeSelect>
        </QueryBarItem>
        <QueryBarItem label="TAPD链接" :label-width="80">
          <NInput
            v-model:value="queryItems.tapd_url"
            style="width: 300px;"
            placeholder="请输入TAPD需求链接"
            clearable
            @keypress.enter="handleSearch"
          ></NInput>
        </QueryBarItem>
      </template>
      </CrudTable>
    </div>

    <!-- 新增/编辑 弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
        :rules="reqsRules"
      >
      <NFormItem label="关联项目" path="project_id">
          <NTreeSelect
            v-model:value="modalForm.project_id"
            :options="projOption"
            key-field="key"
            label-field="label"
            value-field="value"
            placeholder="请选择项目"
            clearable
            default-expand-all
            :disabled="isDisabled"
          ></NTreeSelect>
        </NFormItem>
        <NFormItem label="需求名称" path="name">
          <NInput v-model:value="modalForm.name" clearable placeholder="请输入需求名称" />
        </NFormItem>
        <NFormItem label="需求内容" path="description">
          <NInput v-model:value="modalForm.description" clearable placeholder="请输入需求内容" />
        </NFormItem>
        <NFormItem label="备注" path="remark">
          <NInput v-model:value="modalForm.remark" type="textarea" clearable />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>
